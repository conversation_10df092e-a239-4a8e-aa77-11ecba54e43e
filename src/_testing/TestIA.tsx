import { useAIContext } from "../contexts/AIContext";

export const TestIA = () => {
  const { generateResponse, isLoading } = useAIContext();

  const handleTest = async () => {
    const response = await generateResponse("<PERSON><PERSON>, ¿estás listo para jugar?", "yo_pienso");
    console.log(response);
  };

  return (
    <button onClick={handleTest} disabled={isLoading}>
      {isLoading ? "Pensando..." : "Probar IA"}
    </button>
  );
};
