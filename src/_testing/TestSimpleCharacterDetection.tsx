import { useState } from "react";

export const TestSimpleCharacterDetection = () => {
  const [testResult, setTestResult] = useState<string>("");
  const [storedCharacter, setStoredCharacter] = useState<string | null>(null);

  // Función para extraer personaje de texto (copia de la lógica del servicio)
  const extractCharacterFromText = (text: string): string | null => {
    const patterns = [
      /Personaje:\s*([^.!?\n]+)/i,
      /(?:he pensado en|elegido|seleccionado|mi personaje es)\s*:?\s*([^.!?\n]+)/i,
      /(?:character):\s*([^.!?\n]+)/i,
      /(?:estoy pensando en)\s*([^.!?\n]+)/i,
      /(?:mi elección es)\s*([^.!?\n]+)/i,
      /(?:he elegido a)\s*([^.!?\n]+)/i,
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match && match[1]) {
        const character = match[1].trim();
        return character.replace(/["""''`]+$/, '').trim();
      }
    }
    return null;
  };

  const handleTestExtraction = () => {
    const testTexts = [
      "¡Perfecto! Ya he pensado en alguien del mundo del entretenimiento. Puedes empezar a hacerme preguntas y yo responderé solo con 'Sí', 'No' o 'No lo sé'. ¡A ver si puedes adivinarlo! Personaje: Leonardo DiCaprio",
      "He elegido a Brad Pitt para este juego.",
      "Mi personaje es: Jennifer Lawrence",
      "Estoy pensando en Tom Hanks",
      "Character: Robert Downey Jr.",
    ];

    const results: string[] = [];
    
    testTexts.forEach((text, index) => {
      const extracted = extractCharacterFromText(text);
      results.push(`Test ${index + 1}: ${extracted || "No detectado"}`);
    });

    setTestResult(results.join("\n"));
  };

  const handleTestLocalStorage = () => {
    try {
      // Simular guardado
      const testCharacter = "Leonardo DiCaprio";
      localStorage.setItem("enygma_current_character", testCharacter);
      localStorage.setItem("enygma_character_timestamp", new Date().toISOString());
      
      // Leer de vuelta
      const stored = localStorage.getItem("enygma_current_character");
      setStoredCharacter(stored);
      setTestResult(`✅ Guardado y leído: ${stored}`);
    } catch (error) {
      setTestResult(`❌ Error: ${error instanceof Error ? error.message : "Error desconocido"}`);
    }
  };

  const handleClearStorage = () => {
    try {
      localStorage.removeItem("enygma_current_character");
      localStorage.removeItem("enygma_character_timestamp");
      setStoredCharacter(null);
      setTestResult("🗑️ Storage limpiado");
    } catch (error) {
      setTestResult(`❌ Error: ${error instanceof Error ? error.message : "Error desconocido"}`);
    }
  };

  const updateStoredCharacter = () => {
    try {
      const stored = localStorage.getItem("enygma_current_character");
      setStoredCharacter(stored);
      setTestResult("🔄 Estado actualizado");
    } catch (error) {
      setTestResult(`❌ Error: ${error instanceof Error ? error.message : "Error desconocido"}`);
    }
  };

  return (
    <div
      style={{
        position: "fixed",
        top: "20px",
        left: "20px",
        width: "350px",
        backgroundColor: "white",
        border: "2px solid #16a34a",
        borderRadius: "12px",
        padding: "16px",
        boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
        zIndex: 9997,
        maxHeight: "80vh",
        overflowY: "auto",
      }}
    >
      <h3 style={{ margin: "0 0 12px 0", color: "#16a34a", fontSize: "16px" }}>
        🧪 Test Simple - Detección de Personajes
      </h3>

      {/* Estado actual */}
      <div
        style={{
          backgroundColor: "#f0fdf4",
          border: "1px solid #16a34a",
          borderRadius: "6px",
          padding: "10px",
          marginBottom: "12px",
          fontSize: "12px",
        }}
      >
        <div><strong>Personaje en Storage:</strong> {storedCharacter || "Ninguno"}</div>
      </div>

      {/* Controles */}
      <div style={{ display: "flex", flexDirection: "column", gap: "8px", marginBottom: "12px" }}>
        <button
          onClick={handleTestExtraction}
          style={{
            padding: "8px 12px",
            backgroundColor: "#3b82f6",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: "pointer",
            fontSize: "12px",
            fontWeight: "500",
          }}
        >
          🔍 Test Extracción de Texto
        </button>

        <button
          onClick={handleTestLocalStorage}
          style={{
            padding: "8px 12px",
            backgroundColor: "#16a34a",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: "pointer",
            fontSize: "12px",
            fontWeight: "500",
          }}
        >
          💾 Test LocalStorage
        </button>

        <div style={{ display: "flex", gap: "6px" }}>
          <button
            onClick={updateStoredCharacter}
            style={{
              flex: 1,
              padding: "6px 8px",
              backgroundColor: "#f59e0b",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
              fontSize: "11px",
            }}
          >
            🔄 Actualizar
          </button>

          <button
            onClick={handleClearStorage}
            style={{
              flex: 1,
              padding: "6px 8px",
              backgroundColor: "#dc2626",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
              fontSize: "11px",
            }}
          >
            🗑️ Limpiar
          </button>
        </div>
      </div>

      {/* Resultado */}
      {testResult && (
        <div
          style={{
            backgroundColor: "#f8fafc",
            border: "1px solid #e2e8f0",
            borderRadius: "6px",
            padding: "10px",
            fontSize: "11px",
            color: "#334155",
            fontFamily: "monospace",
            whiteSpace: "pre-line",
            maxHeight: "150px",
            overflowY: "auto",
          }}
        >
          {testResult}
        </div>
      )}

      {/* Info */}
      <div
        style={{
          backgroundColor: "#f0f9ff",
          border: "1px solid #3b82f6",
          borderRadius: "6px",
          padding: "8px",
          marginTop: "12px",
          fontSize: "10px",
          color: "#1e40af",
        }}
      >
        <strong>💡 Test independiente:</strong> No depende de contextos, solo prueba la lógica de detección y localStorage.
      </div>
    </div>
  );
};
