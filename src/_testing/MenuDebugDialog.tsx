import { TestMovistarPlus } from "./TestMovistarPlus";
import { TestSpeech } from "./TestSpeech";
import { TestTranscription } from "./TestTranscription";

export const MenuDebugDialog: React.FC = ({}) => {
  return (
    <div className="debug-content">
      <h3>Debug Menu</h3>
      <p>Testing tools for development</p>

      {import.meta.env.VITE_DEBUG_MOVISTARPLUS === "true" && (
        <TestMovistarPlus />
      )}

      {import.meta.env.VITE_DEBUG_SPEECH === "true" && <TestSpeech />}

      {import.meta.env.VITE_DEBUG_TRANSCRIPTION === "true" && (
        <TestTranscription />
      )}
    </div>
  );
};
