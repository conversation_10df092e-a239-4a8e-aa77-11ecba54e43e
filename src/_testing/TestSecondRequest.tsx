import { useState, useEffect } from "react";

export const TestSecondRequest = () => {
  const [logs, setLogs] = useState<string[]>([]);
  const [isListening, setIsListening] = useState(false);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, `[${timestamp}] ${message}`]);
  };

  useEffect(() => {
    if (!isListening) return;

    // Interceptar logs de consola para mostrar en el componente
    const originalLog = console.log;
    const originalWarn = console.warn;
    const originalError = console.error;

    console.log = (...args) => {
      const message = args.join(' ');
      if (message.includes('🎭') || message.includes('Personaje') || message.includes('específico')) {
        addLog(`LOG: ${message}`);
      }
      originalLog.apply(console, args);
    };

    console.warn = (...args) => {
      const message = args.join(' ');
      if (message.includes('🎭') || message.includes('Personaje') || message.includes('específico')) {
        addLog(`WARN: ${message}`);
      }
      originalWarn.apply(console, args);
    };

    console.error = (...args) => {
      const message = args.join(' ');
      if (message.includes('🎭') || message.includes('Personaje') || message.includes('específico')) {
        addLog(`ERROR: ${message}`);
      }
      originalError.apply(console, args);
    };

    // Escuchar evento de actualización de personaje
    const handleCharacterUpdate = (event: CustomEvent) => {
      addLog(`🎉 EVENTO: Personaje actualizado a "${event.detail.character}"`);
    };

    window.addEventListener('characterUpdated', handleCharacterUpdate as EventListener);

    return () => {
      console.log = originalLog;
      console.warn = originalWarn;
      console.error = originalError;
      window.removeEventListener('characterUpdated', handleCharacterUpdate as EventListener);
    };
  }, [isListening]);

  const handleStartListening = () => {
    setIsListening(true);
    setLogs([]);
    addLog("🎧 Iniciando monitoreo de logs...");
  };

  const handleStopListening = () => {
    setIsListening(false);
    addLog("⏹️ Monitoreo detenido");
  };

  const handleClearLogs = () => {
    setLogs([]);
  };

  const handleTestGenericResponse = () => {
    addLog("🧪 Simulando respuesta genérica...");
    
    // Simular el evento que se dispararía con una respuesta genérica
    setTimeout(() => {
      addLog("⚠️ Personaje genérico detectado: 'alguien del mundo del entretenimiento'");
      addLog("⏳ Programando segunda petición en 3 segundos...");
      
      setTimeout(() => {
        addLog("🔄 Ejecutando segunda petición...");
        addLog("✅ Personaje específico obtenido: 'Leonardo DiCaprio'");
        
        // Simular evento de actualización
        window.dispatchEvent(new CustomEvent('characterUpdated', { 
          detail: { character: 'Leonardo DiCaprio' } 
        }));
      }, 3000);
    }, 500);
  };

  return (
    <div
      style={{
        position: "fixed",
        bottom: "20px",
        left: "20px",
        width: "400px",
        backgroundColor: "white",
        border: "2px solid #8b5cf6",
        borderRadius: "12px",
        padding: "16px",
        boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
        zIndex: 9996,
        maxHeight: "60vh",
        overflowY: "auto",
      }}
    >
      <h3 style={{ margin: "0 0 12px 0", color: "#8b5cf6", fontSize: "16px" }}>
        🔄 Test Segunda Petición
      </h3>

      {/* Controles */}
      <div style={{ display: "flex", gap: "8px", marginBottom: "12px", flexWrap: "wrap" }}>
        <button
          onClick={isListening ? handleStopListening : handleStartListening}
          style={{
            padding: "6px 12px",
            backgroundColor: isListening ? "#dc2626" : "#16a34a",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: "pointer",
            fontSize: "12px",
            fontWeight: "500",
          }}
        >
          {isListening ? "⏹️ Parar" : "🎧 Monitorear"}
        </button>

        <button
          onClick={handleTestGenericResponse}
          disabled={!isListening}
          style={{
            padding: "6px 12px",
            backgroundColor: !isListening ? "#9ca3af" : "#f59e0b",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: !isListening ? "not-allowed" : "pointer",
            fontSize: "12px",
            fontWeight: "500",
          }}
        >
          🧪 Simular Genérico
        </button>

        <button
          onClick={handleClearLogs}
          style={{
            padding: "6px 12px",
            backgroundColor: "#6b7280",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: "pointer",
            fontSize: "12px",
          }}
        >
          🗑️ Limpiar
        </button>
      </div>

      {/* Estado */}
      <div
        style={{
          backgroundColor: isListening ? "#f0fdf4" : "#f8fafc",
          border: `1px solid ${isListening ? "#16a34a" : "#e2e8f0"}`,
          borderRadius: "6px",
          padding: "8px",
          marginBottom: "12px",
          fontSize: "12px",
          color: isListening ? "#15803d" : "#64748b",
        }}
      >
        <strong>Estado:</strong> {isListening ? "🟢 Monitoreando" : "🔴 Detenido"}
      </div>

      {/* Logs */}
      <div
        style={{
          backgroundColor: "#1f2937",
          color: "#f9fafb",
          borderRadius: "6px",
          padding: "10px",
          fontSize: "11px",
          fontFamily: "monospace",
          maxHeight: "200px",
          overflowY: "auto",
          border: "1px solid #374151",
        }}
      >
        {logs.length === 0 ? (
          <div style={{ color: "#9ca3af", fontStyle: "italic" }}>
            No hay logs aún. Haz clic en "Monitorear" y luego prueba la detección de personajes.
          </div>
        ) : (
          logs.map((log, index) => (
            <div key={index} style={{ marginBottom: "4px", lineHeight: "1.4" }}>
              {log}
            </div>
          ))
        )}
      </div>

      {/* Instrucciones */}
      <div
        style={{
          backgroundColor: "#fef3c7",
          border: "1px solid #f59e0b",
          borderRadius: "6px",
          padding: "8px",
          marginTop: "12px",
          fontSize: "10px",
          color: "#92400e",
        }}
      >
        <strong>💡 Instrucciones:</strong>
        <ul style={{ margin: "4px 0 0 0", paddingLeft: "16px" }}>
          <li>Haz clic en "Monitorear" para empezar</li>
          <li>Usa el panel de detección de personajes para probar</li>
          <li>Observa los logs en tiempo real</li>
          <li>Usa "Simular Genérico" para probar el flujo completo</li>
        </ul>
      </div>
    </div>
  );
};
