import React, { useState } from "react";
import { useMovistarPlusContext } from "../contexts/MovistarPlusContext";
import { movistarPlusService } from "../services/MovistarPlusService";

export const TestMovistarPlus: React.FC = () => {
  const {
    isLoading,
    error,
    getMovistarPlusFilms,
    searchByTitle,
    setFilmToLaunch,
    reset,
    testConnection,
    isConfigured,
    filmToLaunch,
  } = useMovistarPlusContext();

  const [searchTerm, setSearchTerm] = useState<string>("Avengers");
  const [results, setResults] = useState<any>(null);
  const [connectionStatus, setConnectionStatus] = useState<boolean | null>(
    null,
  );

  // ========== TEST FUNCTIONS ==========
  const handleTestSearch = async () => {
    try {
      const result = await getMovistarPlusFilms({
        title: searchTerm,
        recommendation: `Test search for ${searchTerm}`,
      });
      setResults(result);
    } catch (error) {
      console.error("Error en test de búsqueda:", error);
    }
  };

  const handleTestSearchByTitle = async () => {
    try {
      const result = await searchByTitle(searchTerm);
      setResults(result);
    } catch (error) {
      console.error("Error en búsqueda por título:", error);
    }
  };

  const handleTestConnection = async () => {
    const status = await testConnection();
    setConnectionStatus(status);
  };

  const handleDirectServiceTest = async () => {
    try {
      console.log("🧪 Test directo del servicio...");
      const result = await movistarPlusService.getMovistarPlusFilms({
        title: searchTerm,
        recommendation: "Test directo",
      });
      setResults(result);
      console.log("Resultado directo:", result);
    } catch (error) {
      console.error("Error en test directo:", error);
    }
  };

  const handleSetFilm = () => {
    setFilmToLaunch(searchTerm);
  };

  const handleClearFilm = () => {
    setFilmToLaunch(null);
  };

  const handleReset = () => {
    reset();
    setResults(null);
    setConnectionStatus(null);
  };

  // ========== SERVICE STATS ==========
  const serviceStats = movistarPlusService.getStats();

  return (
    <div
      style={{
        padding: "20px",
        margin: "20px",
        border: "2px solid #e5e7eb",
        borderRadius: "12px",
        backgroundColor: "#f9fafb",
        fontFamily: "system-ui, sans-serif",
      }}
    >
      <h2
        style={{
          color: "#1f2937",
          marginBottom: "20px",
          fontSize: "24px",
          fontWeight: "bold",
        }}
      >
        🎬 Test MovistarPlus Service
      </h2>

      {/* Configuration Status */}
      <div
        style={{
          backgroundColor: isConfigured() ? "#dcfce7" : "#fef2f2",
          border: `1px solid ${isConfigured() ? "#16a34a" : "#dc2626"}`,
          borderRadius: "8px",
          padding: "12px",
          marginBottom: "20px",
        }}
      >
        <h3
          style={{
            margin: "0 0 8px 0",
            color: isConfigured() ? "#15803d" : "#dc2626",
          }}
        >
          {isConfigured() ? "✅ API Configurada" : "❌ API No Configurada"}
        </h3>
        <div style={{ fontSize: "12px", color: "#6b7280" }}>
          <div>
            <strong>Base URL:</strong>{" "}
            {serviceStats.baseURL || "No configurada"}
          </div>
          <div>
            <strong>Timeout:</strong> {serviceStats.timeout}ms
          </div>
          <div>
            <strong>Retries:</strong> {serviceStats.retries}
          </div>
        </div>
      </div>

      {/* Search Input */}
      <div style={{ marginBottom: "20px" }}>
        <label
          style={{
            display: "block",
            marginBottom: "8px",
            fontWeight: "600",
            color: "#374151",
          }}
        >
          Término de búsqueda:
        </label>
        <input
          type="text"
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="Ej: Avengers, Matrix, etc."
          style={{
            width: "100%",
            padding: "10px",
            border: "1px solid #d1d5db",
            borderRadius: "6px",
            fontSize: "14px",
          }}
        />
      </div>

      {/* Action Buttons */}
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
          gap: "10px",
          marginBottom: "20px",
        }}
      >
        <button
          onClick={handleTestSearch}
          disabled={isLoading || !isConfigured()}
          style={{
            padding: "10px 16px",
            backgroundColor: isLoading ? "#9ca3af" : "#3b82f6",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: isLoading ? "not-allowed" : "pointer",
            fontSize: "14px",
            fontWeight: "500",
          }}
        >
          {isLoading ? "⏳ Buscando..." : "🔍 Buscar (Context)"}
        </button>

        <button
          onClick={handleTestSearchByTitle}
          disabled={isLoading || !isConfigured()}
          style={{
            padding: "10px 16px",
            backgroundColor: isLoading ? "#9ca3af" : "#10b981",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: isLoading ? "not-allowed" : "pointer",
            fontSize: "14px",
            fontWeight: "500",
          }}
        >
          📽️ Búsqueda por Título
        </button>

        <button
          onClick={handleDirectServiceTest}
          disabled={isLoading || !isConfigured()}
          style={{
            padding: "10px 16px",
            backgroundColor: isLoading ? "#9ca3af" : "#8b5cf6",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: isLoading ? "not-allowed" : "pointer",
            fontSize: "14px",
            fontWeight: "500",
          }}
        >
          🧪 Test Directo (Service)
        </button>

        <button
          onClick={handleTestConnection}
          disabled={isLoading || !isConfigured()}
          style={{
            padding: "10px 16px",
            backgroundColor: isLoading ? "#9ca3af" : "#f59e0b",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: isLoading ? "not-allowed" : "pointer",
            fontSize: "14px",
            fontWeight: "500",
          }}
        >
          🔗 Test Conexión
        </button>
      </div>

      {/* Film Management */}
      <div
        style={{
          backgroundColor: "#eff6ff",
          border: "1px solid #3b82f6",
          borderRadius: "8px",
          padding: "16px",
          marginBottom: "20px",
        }}
      >
        <h4 style={{ margin: "0 0 12px 0", color: "#1e40af" }}>
          🎬 Gestión de Película
        </h4>
        <div
          style={{ fontSize: "14px", marginBottom: "12px", color: "#374151" }}
        >
          <strong>Película actual:</strong> {filmToLaunch || "Ninguna"}
        </div>
        <div style={{ display: "flex", gap: "10px", flexWrap: "wrap" }}>
          <button
            onClick={handleSetFilm}
            style={{
              padding: "8px 16px",
              backgroundColor: "#16a34a",
              color: "white",
              border: "none",
              borderRadius: "6px",
              cursor: "pointer",
              fontSize: "12px",
            }}
          >
            ✅ Establecer Película
          </button>
          <button
            onClick={handleClearFilm}
            style={{
              padding: "8px 16px",
              backgroundColor: "#dc2626",
              color: "white",
              border: "none",
              borderRadius: "6px",
              cursor: "pointer",
              fontSize: "12px",
            }}
          >
            ❌ Limpiar Película
          </button>
        </div>
      </div>

      {/* Reset Button */}
      <button
        onClick={handleReset}
        style={{
          padding: "10px 20px",
          backgroundColor: "#6b7280",
          color: "white",
          border: "none",
          borderRadius: "6px",
          cursor: "pointer",
          fontSize: "14px",
          fontWeight: "500",
          marginBottom: "20px",
        }}
      >
        🧹 Reset Todo
      </button>

      {/* Connection Status */}
      {connectionStatus !== null && (
        <div
          style={{
            backgroundColor: connectionStatus ? "#dcfce7" : "#fef2f2",
            border: `1px solid ${connectionStatus ? "#16a34a" : "#dc2626"}`,
            borderRadius: "8px",
            padding: "12px",
            marginBottom: "20px",
          }}
        >
          <h4
            style={{
              margin: "0",
              color: connectionStatus ? "#15803d" : "#dc2626",
            }}
          >
            {connectionStatus ? "✅ Conexión Exitosa" : "❌ Conexión Fallida"}
          </h4>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div
          style={{
            backgroundColor: "#fef2f2",
            border: "1px solid #dc2626",
            borderRadius: "8px",
            padding: "12px",
            marginBottom: "20px",
            color: "#dc2626",
          }}
        >
          <h4 style={{ margin: "0 0 8px 0" }}>❌ Error:</h4>
          <div style={{ fontSize: "14px" }}>{error}</div>
        </div>
      )}

      {/* Results Display */}
      {results && (
        <div
          style={{
            backgroundColor: "#f0f9ff",
            border: "1px solid #0ea5e9",
            borderRadius: "8px",
            padding: "16px",
          }}
        >
          <h4 style={{ margin: "0 0 12px 0", color: "#0369a1" }}>
            📊 Resultados:
          </h4>

          {/* Success Status */}
          <div
            style={{
              marginBottom: "12px",
              padding: "8px",
              backgroundColor: results.success ? "#dcfce7" : "#fef2f2",
              borderRadius: "4px",
              fontSize: "14px",
              color: results.success ? "#15803d" : "#dc2626",
            }}
          >
            <strong>Estado:</strong>{" "}
            {results.success ? "✅ Exitoso" : "❌ Error"}
            {results.error && <div>Error: {results.error}</div>}
          </div>

          {/* Titles */}
          {results.titles && results.titles.length > 0 && (
            <div style={{ marginBottom: "12px" }}>
              <strong style={{ color: "#374151" }}>
                Títulos encontrados ({results.titles.length}):
              </strong>
              <ul style={{ marginTop: "8px", paddingLeft: "20px" }}>
                {results.titles
                  .slice(0, 10)
                  .map((title: string, index: number) => (
                    <li
                      key={index}
                      style={{
                        marginBottom: "4px",
                        fontSize: "14px",
                        color: "#4b5563",
                      }}
                    >
                      {title}
                    </li>
                  ))}
                {results.titles.length > 10 && (
                  <li style={{ fontStyle: "italic", color: "#6b7280" }}>
                    ... y {results.titles.length - 10} más
                  </li>
                )}
              </ul>
            </div>
          )}

          {/* Raw Data */}
          <details style={{ marginTop: "12px" }}>
            <summary
              style={{
                cursor: "pointer",
                fontWeight: "600",
                color: "#374151",
              }}
            >
              📄 Ver datos completos
            </summary>
            <pre
              style={{
                backgroundColor: "#f8fafc",
                border: "1px solid #e2e8f0",
                borderRadius: "4px",
                padding: "12px",
                fontSize: "12px",
                overflow: "auto",
                maxHeight: "300px",
                marginTop: "8px",
                color: "#475569",
              }}
            >
              {JSON.stringify(results, null, 2)}
            </pre>
          </details>
        </div>
      )}

      {/* Service Stats */}
      <details style={{ marginTop: "20px" }}>
        <summary
          style={{
            cursor: "pointer",
            fontWeight: "600",
            color: "#374151",
            padding: "10px",
            backgroundColor: "#f3f4f6",
            borderRadius: "6px",
          }}
        >
          ⚙️ Estadísticas del Servicio
        </summary>
        <div
          style={{
            backgroundColor: "#f8fafc",
            border: "1px solid #e2e8f0",
            borderRadius: "6px",
            padding: "16px",
            marginTop: "8px",
          }}
        >
          <pre
            style={{
              fontSize: "12px",
              color: "#475569",
              margin: 0,
              whiteSpace: "pre-wrap",
            }}
          >
            {JSON.stringify(serviceStats, null, 2)}
          </pre>
        </div>
      </details>

      {/* Debug Info */}
      <div
        style={{
          marginTop: "20px",
          padding: "12px",
          backgroundColor: "#fef3c7",
          border: "1px solid #f59e0b",
          borderRadius: "6px",
          fontSize: "12px",
          color: "#92400e",
        }}
      >
        <strong>💡 Debug Info:</strong>
        <br />
        • Variables de entorno: VITE_MOVISTAR_API_URL
        <br />
        • El servicio incluye retry automático y extracción inteligente de
        títulos
        <br />
        • Los datos se persisten en localStorage para la película seleccionada
        <br />• Usa los botones de arriba para probar diferentes funcionalidades
      </div>
    </div>
  );
};
