import React, { useState, useEffect } from "react";
import { useAudioContext } from "../contexts/AudioContext";
import { speechService } from "../services/SpeechService";

export const TestSpeech: React.FC = () => {
  const {
    output: {
      isLocutionActivated,
      isAudioOnPlay,
      isConfiguring,
      availableVoices,
      currentVoiceId,
    },
    getSpeech,
    onClickSoundBtn,
    configVoice,
    toSpeech,
    setSpeech,
    speak,
    speakWithAutoConfig,
  } = useAudioContext();

  const [testText, setTestText] = useState<string>(
    "Hola, soy Aura. Este es un test del sistema de voces de Azure.",
  );
  const [selectedGenre, setSelectedGenre] = useState<string>("female");
  const [audioUrl, setAudioUrl] = useState<string>("");
  const [connectionStatus, setConnectionStatus] = useState<boolean | null>(
    null,
  );

  const [logs, setLogs] = useState<string[]>([]);
  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs((prev) => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)]);
  };

  // ========== EFECTOS ==========
  useEffect(() => {
    addLog("🚀 Speech Service Tester iniciado");
    addLog(
      `📡 API URL: ${import.meta.env.VITE_SPEECH_API_URL || "NO CONFIGURADA"}`,
    );
    addLog(
      `🔑 API Key: ${import.meta.env.VITE_SPEECH_API_KEY ? "CONFIGURADA" : "NO CONFIGURADA"}`,
    );

    // Obtener estadísticas del servicio
    updateServiceStats();
  }, []);

  useEffect(() => {
    const statusText =
      isAudioOnPlay === true
        ? "REPRODUCIENDO"
        : isAudioOnPlay === false
          ? "PAUSADO"
          : "DESACTIVADO";
    addLog(`🔊 Estado audio: ${statusText}`);
  }, [isAudioOnPlay]);

  useEffect(() => {
    if (isLocutionActivated) {
      addLog(`✅ Locución activada - Voz actual: ${currentVoiceId}`);
      addLog(`📋 Voces disponibles: ${availableVoices.length}`);
    }
  }, [isLocutionActivated, currentVoiceId, availableVoices]);

  // ========== FUNCIONES ==========
  const updateServiceStats = () => {
    try {
      const stats = speechService.getStats();
      addLog(`📊 Stats del servicio actualizadas`);
      console.log("Service Stats:", stats);
    } catch (error) {
      addLog(`❌ Error obteniendo stats: ${error}`);
    }
  };

  const testAPIConnection = async () => {
    addLog("🌐 Testeando conectividad con Azure API...");
    setConnectionStatus(null);

    if (!import.meta.env.VITE_SPEECH_API_URL) {
      addLog("❌ VITE_SPEECH_API_URL no está configurada");
      setConnectionStatus(false);
      return;
    }

    if (!import.meta.env.VITE_SPEECH_API_KEY) {
      addLog("❌ VITE_SPEECH_API_KEY no está configurada");
      setConnectionStatus(false);
      return;
    }

    try {
      const success = await configVoice("female");
      if (success) {
        addLog("✅ Conectividad con Azure API confirmada");
        setConnectionStatus(true);
      } else {
        addLog("⚠️ API responde pero no retorna voces");
        setConnectionStatus(false);
      }
    } catch (error) {
      addLog(`❌ Error de conectividad Azure: ${error}`);
      setConnectionStatus(false);
    }
  };

  const testConfigVoice = async () => {
    try {
      addLog(`⚙️ Configurando voz género: ${selectedGenre}...`);
      const success = await configVoice(selectedGenre);
      if (success) {
        addLog("✅ Voz Azure configurada exitosamente");
        updateServiceStats();
      } else {
        addLog("❌ Error al configurar voz Azure");
      }
    } catch (error) {
      addLog(`❌ Error configurando voz: ${error}`);
    }
  };

  const testGetSpeech = async () => {
    try {
      addLog("📥 Obteniendo audio desde Azure...");
      const url = await getSpeech(testText);
      setAudioUrl(url);
      addLog("✅ Audio obtenido exitosamente desde Azure");
      addLog(`🔗 Audio URL: ${url.substring(0, 50)}...`);
    } catch (error) {
      addLog(`❌ Error obteniendo audio: ${error}`);
    }
  };

  const testSpeak = async () => {
    try {
      addLog("🎤 Iniciando speak() simplificado...");
      await speak(testText);
      addLog("✅ Speak completado exitosamente");
    } catch (error) {
      addLog(`❌ Error en speak(): ${error}`);
    }
  };

  const testSpeakWithAutoConfig = async () => {
    try {
      addLog("🎤 Iniciando speakWithAutoConfig()...");
      await speakWithAutoConfig(testText, selectedGenre);
      addLog("✅ SpeakWithAutoConfig completado exitosamente");
      updateServiceStats();
    } catch (error) {
      addLog(`❌ Error en speakWithAutoConfig(): ${error}`);
    }
  };

  const testDirectService = async () => {
    try {
      addLog("🧪 Test directo del servicio...");

      // Test directo del servicio
      if (!speechService.getCurrentVoiceId()) {
        addLog("🔧 Configurando voz directamente en el servicio...");
        const configured = await speechService.configVoice(selectedGenre);
        if (!configured) {
          throw new Error("No se pudo configurar la voz en el servicio");
        }
      }

      await speechService.speak(testText);
      addLog("✅ Test directo del servicio exitoso");
      updateServiceStats();
    } catch (error) {
      addLog(`❌ Error en test directo: ${error}`);
    }
  };

  const testPlayAudio = () => {
    if (audioUrl) {
      addLog("▶️ Reproduciendo audio...");
      toSpeech(audioUrl);
    } else {
      addLog("❌ No hay audio disponible para reproducir");
    }
  };

  const testSetAudio = () => {
    if (audioUrl) {
      addLog("📋 Cargando audio sin reproducir...");
      setSpeech(audioUrl);
    } else {
      addLog("❌ No hay audio disponible para cargar");
    }
  };

  const testFullWorkflow = async () => {
    addLog("🚀 Iniciando test completo Azure...");

    try {
      // 1. Test conectividad
      addLog("1️⃣ Verificando conectividad...");
      await testAPIConnection();
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 2. Configurar voz
      addLog("2️⃣ Configurando voz Azure...");
      await testConfigVoice();
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 3. Obtener audio
      addLog("3️⃣ Obteniendo audio...");
      await testGetSpeech();
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // 4. Reproducir
      addLog("4️⃣ Reproduciendo audio...");
      if (audioUrl) {
        testPlayAudio();
      }

      addLog("🏁 Test completo finalizado exitosamente");
    } catch (error) {
      addLog(`❌ Error en test completo: ${error}`);
    }
  };

  const resetSystem = () => {
    setIsLocutionActivated(false);
    setIsAudioOnPlay(null);
    setAudioUrl("");
    setConnectionStatus(null);

    // Reset del servicio
    speechService.reset();

    addLog("🔄 Sistema reseteado completamente");
    updateServiceStats();
  };

  const clearLogs = () => {
    setLogs([]);
    addLog("🧹 Logs limpiados");
  };

  // ========== RENDER ==========
  const isConfigured = Boolean(
    import.meta.env.VITE_SPEECH_API_URL && import.meta.env.VITE_SPEECH_API_KEY,
  );
  const serviceStats = speechService.getStats();

  const predefinedTexts = [
    "Hola, soy Aura. ¿Cómo estás?",
    "Bienvenido a Enygma, ¿lo adivinas?",
    "¡Excelente! Has adivinado correctamente.",
    "Lo siento, esa no es la respuesta correcta.",
    "¿Tu personaje aparece en películas?",
    "Creo que estás pensando en Superman. ¿Es correcto?",
  ];

  return (
    <div
      style={{
        padding: "20px",
        margin: "20px",
        border: "2px solid #e5e7eb",
        borderRadius: "12px",
        backgroundColor: "#f9fafb",
        fontFamily: "system-ui, sans-serif",
      }}
    >
      <h2
        style={{
          color: "#1f2937",
          marginBottom: "20px",
          fontSize: "24px",
          fontWeight: "bold",
        }}
      >
        🎤 Test Speech Service (Azure)
      </h2>

      {/* Configuration Status */}
      <div
        style={{
          backgroundColor: isConfigured ? "#dcfce7" : "#fef2f2",
          border: `1px solid ${isConfigured ? "#16a34a" : "#dc2626"}`,
          borderRadius: "8px",
          padding: "12px",
          marginBottom: "20px",
        }}
      >
        <h3
          style={{
            margin: "0 0 8px 0",
            color: isConfigured ? "#15803d" : "#dc2626",
          }}
        >
          {isConfigured ? "✅ API Configurada" : "❌ API No Configurada"}
        </h3>
        <div
          style={{
            fontSize: "12px",
            color: "#6b7280",
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
            gap: "8px",
          }}
        >
          <div>
            <strong>API URL:</strong>{" "}
            {import.meta.env.VITE_SPEECH_API_URL ? "✅" : "❌"}
          </div>
          <div>
            <strong>API Key:</strong>{" "}
            {import.meta.env.VITE_SPEECH_API_KEY ? "✅" : "❌"}
          </div>
          <div>
            <strong>Locución:</strong>{" "}
            {isLocutionActivated ? "✅ Activa" : "❌ Inactiva"}
          </div>
          <div>
            <strong>Configurando:</strong> {isConfiguring ? "⏳ Sí" : "✅ No"}
          </div>
          <div>
            <strong>Audio:</strong>{" "}
            {isAudioOnPlay === true
              ? "🔊 Sonando"
              : isAudioOnPlay === false
                ? "⏸️ Pausado"
                : "🔇 Sin audio"}
          </div>
          <div>
            <strong>Voces:</strong> {availableVoices.length}
          </div>
        </div>
      </div>

      {/* Connection Status */}
      {connectionStatus !== null && (
        <div
          style={{
            backgroundColor: connectionStatus ? "#dcfce7" : "#fef2f2",
            border: `1px solid ${connectionStatus ? "#16a34a" : "#dc2626"}`,
            borderRadius: "8px",
            padding: "12px",
            marginBottom: "20px",
          }}
        >
          <h4
            style={{
              margin: "0",
              color: connectionStatus ? "#15803d" : "#dc2626",
            }}
          >
            {connectionStatus
              ? "✅ Conexión Azure Exitosa"
              : "❌ Conexión Azure Fallida"}
          </h4>
        </div>
      )}

      {/* Test Configuration */}
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "1fr 1fr",
          gap: "20px",
          marginBottom: "20px",
        }}
      >
        {/* Text Input */}
        <div>
          <label
            style={{
              display: "block",
              marginBottom: "8px",
              fontWeight: "600",
              color: "#374151",
            }}
          >
            📝 Texto de prueba:
          </label>
          <textarea
            value={testText}
            onChange={(e) => setTestText(e.target.value)}
            rows={3}
            style={{
              width: "100%",
              padding: "10px",
              border: "1px solid #d1d5db",
              borderRadius: "6px",
              fontSize: "14px",
              resize: "vertical",
            }}
            placeholder="Ingresa el texto para convertir a voz..."
          />
        </div>

        {/* Genre Selection */}
        <div>
          <label
            style={{
              display: "block",
              marginBottom: "8px",
              fontWeight: "600",
              color: "#374151",
            }}
          >
            👤 Género de voz:
          </label>
          <select
            value={selectedGenre}
            onChange={(e) => setSelectedGenre(e.target.value)}
            style={{
              width: "100%",
              padding: "10px",
              border: "1px solid #d1d5db",
              borderRadius: "6px",
              fontSize: "14px",
              marginBottom: "12px",
            }}
          >
            <option value="female">👩 Femenino</option>
            <option value="male">👨 Masculino</option>
          </select>

          {/* Current Voice Info */}
          {isLocutionActivated && (
            <div
              style={{
                backgroundColor: "#f0f9ff",
                border: "1px solid #0ea5e9",
                borderRadius: "6px",
                padding: "12px",
              }}
            >
              <h4
                style={{
                  margin: "0 0 8px 0",
                  fontSize: "14px",
                  color: "#0369a1",
                }}
              >
                🎭 Voz Actual:
              </h4>
              <div style={{ fontSize: "12px", color: "#374151" }}>
                <div>
                  <strong>ID:</strong> {currentVoiceId || "No seleccionada"}
                </div>
                <div>
                  <strong>Disponibles:</strong> {availableVoices.length}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Main Test Buttons */}
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(150px, 1fr))",
          gap: "10px",
          marginBottom: "20px",
        }}
      >
        <button
          onClick={testAPIConnection}
          disabled={!isConfigured}
          style={{
            padding: "10px 16px",
            backgroundColor: !isConfigured ? "#9ca3af" : "#6366f1",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: !isConfigured ? "not-allowed" : "pointer",
            fontSize: "14px",
            fontWeight: "500",
          }}
        >
          🌐 Test API
        </button>

        <button
          onClick={testConfigVoice}
          disabled={isConfiguring || !isConfigured}
          style={{
            padding: "10px 16px",
            backgroundColor:
              isConfiguring || !isConfigured ? "#9ca3af" : "#3b82f6",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: isConfiguring || !isConfigured ? "not-allowed" : "pointer",
            fontSize: "14px",
            fontWeight: "500",
          }}
        >
          ⚙️ Config Voz
        </button>

        <button
          onClick={testGetSpeech}
          disabled={!isLocutionActivated}
          style={{
            padding: "10px 16px",
            backgroundColor: !isLocutionActivated ? "#9ca3af" : "#f59e0b",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: !isLocutionActivated ? "not-allowed" : "pointer",
            fontSize: "14px",
            fontWeight: "500",
          }}
        >
          📥 Obtener Audio
        </button>

        <button
          onClick={testSpeak}
          disabled={!isLocutionActivated}
          style={{
            padding: "10px 16px",
            backgroundColor: !isLocutionActivated ? "#9ca3af" : "#10b981",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: !isLocutionActivated ? "not-allowed" : "pointer",
            fontSize: "14px",
            fontWeight: "500",
          }}
        >
          🎤 Speak
        </button>

        <button
          onClick={testSpeakWithAutoConfig}
          disabled={!isConfigured}
          style={{
            padding: "10px 16px",
            backgroundColor: !isConfigured ? "#9ca3af" : "#8b5cf6",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: !isConfigured ? "not-allowed" : "pointer",
            fontSize: "14px",
            fontWeight: "500",
          }}
        >
          ✨ Auto Speak
        </button>

        <button
          onClick={testDirectService}
          disabled={!isConfigured}
          style={{
            padding: "10px 16px",
            backgroundColor: !isConfigured ? "#9ca3af" : "#ec4899",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: !isConfigured ? "not-allowed" : "pointer",
            fontSize: "14px",
            fontWeight: "500",
          }}
        >
          🧪 Test Directo
        </button>
      </div>

      {/* Audio Controls */}
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(120px, 1fr))",
          gap: "10px",
          marginBottom: "20px",
        }}
      >
        <button
          onClick={testPlayAudio}
          disabled={!audioUrl}
          style={{
            padding: "8px 12px",
            backgroundColor: !audioUrl ? "#9ca3af" : "#7c3aed",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: !audioUrl ? "not-allowed" : "pointer",
            fontSize: "12px",
            fontWeight: "500",
          }}
        >
          ▶️ Play Audio
        </button>

        <button
          onClick={testSetAudio}
          disabled={!audioUrl}
          style={{
            padding: "8px 12px",
            backgroundColor: !audioUrl ? "#9ca3af" : "#ea580c",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: !audioUrl ? "not-allowed" : "pointer",
            fontSize: "12px",
            fontWeight: "500",
          }}
        >
          📋 Load Audio
        </button>

        <button
          onClick={onClickSoundBtn}
          disabled={isAudioOnPlay === null}
          style={{
            padding: "8px 12px",
            backgroundColor: isAudioOnPlay === null ? "#9ca3af" : "#0891b2",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: isAudioOnPlay === null ? "not-allowed" : "pointer",
            fontSize: "12px",
            fontWeight: "500",
          }}
        >
          🔄 Toggle Audio
        </button>

        <button
          onClick={testFullWorkflow}
          disabled={isConfiguring || !isConfigured}
          style={{
            padding: "8px 12px",
            backgroundColor:
              isConfiguring || !isConfigured ? "#9ca3af" : "#059669",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: isConfiguring || !isConfigured ? "not-allowed" : "pointer",
            fontSize: "12px",
            fontWeight: "500",
          }}
        >
          🚀 Test Completo
        </button>

        <button
          onClick={resetSystem}
          style={{
            padding: "8px 12px",
            backgroundColor: "#dc2626",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: "pointer",
            fontSize: "12px",
            fontWeight: "500",
          }}
        >
          🔄 Reset
        </button>

        <button
          onClick={clearLogs}
          style={{
            padding: "8px 12px",
            backgroundColor: "#6b7280",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: "pointer",
            fontSize: "12px",
            fontWeight: "500",
          }}
        >
          🗑️ Clear Logs
        </button>
      </div>

      {/* Predefined Texts */}
      <div
        style={{
          backgroundColor: "#f0fdf4",
          border: "1px solid #16a34a",
          borderRadius: "8px",
          padding: "16px",
          marginBottom: "20px",
        }}
      >
        <h4 style={{ margin: "0 0 12px 0", color: "#15803d" }}>
          📋 Textos Predefinidos
        </h4>

        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(200px, 1fr))",
            gap: "8px",
          }}
        >
          {predefinedTexts.map((text, index) => (
            <button
              key={index}
              onClick={() => setTestText(text)}
              style={{
                padding: "6px 12px",
                backgroundColor: "#16a34a",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
                fontSize: "11px",
                fontWeight: "500",
                textAlign: "left",
              }}
            >
              "{text.substring(0, 30)}
              {text.length > 30 ? "..." : ""}"
            </button>
          ))}
        </div>
      </div>

      {/* Audio Player */}
      {audioUrl && (
        <div
          style={{
            backgroundColor: "#eff6ff",
            border: "1px solid #3b82f6",
            borderRadius: "8px",
            padding: "16px",
            marginBottom: "20px",
          }}
        >
          <h4 style={{ margin: "0 0 12px 0", color: "#1e40af" }}>
            🎵 Audio Generado:
          </h4>
          <div
            style={{
              backgroundColor: "white",
              border: "1px solid #e2e8f0",
              borderRadius: "4px",
              padding: "8px",
              fontSize: "11px",
              fontFamily: "monospace",
              wordBreak: "break-all",
              marginBottom: "12px",
              color: "#475569",
            }}
          >
            {audioUrl}
          </div>
          <audio controls style={{ width: "100%" }} src={audioUrl}>
            Tu navegador no soporta el elemento audio.
          </audio>
        </div>
      )}

      {/* Service Stats */}
      <details style={{ marginBottom: "20px" }}>
        <summary
          style={{
            cursor: "pointer",
            fontWeight: "600",
            color: "#374151",
            padding: "10px",
            backgroundColor: "#f3f4f6",
            borderRadius: "6px",
          }}
        >
          ⚙️ Estadísticas del Servicio
        </summary>
        <div
          style={{
            backgroundColor: "#f8fafc",
            border: "1px solid #e2e8f0",
            borderRadius: "6px",
            padding: "16px",
            marginTop: "8px",
          }}
        >
          <pre
            style={{
              fontSize: "12px",
              color: "#475569",
              margin: 0,
              whiteSpace: "pre-wrap",
            }}
          >
            {JSON.stringify(serviceStats, null, 2)}
          </pre>
        </div>
      </details>

      {/* Logs */}
      <div
        style={{
          backgroundColor: "#1f2937",
          color: "#10b981",
          borderRadius: "8px",
          padding: "16px",
        }}
      >
        <div
          style={{
            display: "flex",
            justifyContent: "space-between",
            alignItems: "center",
            marginBottom: "12px",
          }}
        >
          <h4 style={{ margin: 0, color: "white", fontSize: "16px" }}>
            📋 Speech Service Logs
          </h4>
          <span style={{ color: "#9ca3af", fontSize: "12px" }}>
            {logs.length} entradas
          </span>
        </div>

        <div
          style={{
            maxHeight: "300px",
            overflowY: "auto",
            backgroundColor: "#111827",
            borderRadius: "4px",
            padding: "12px",
          }}
        >
          {logs.length === 0 ? (
            <p style={{ color: "#6b7280", margin: 0, fontSize: "14px" }}>
              No hay logs disponibles...
            </p>
          ) : (
            logs.map((log, index) => (
              <div
                key={index}
                style={{
                  fontSize: "12px",
                  marginBottom: "4px",
                  fontFamily: "monospace",
                }}
              >
                {log}
              </div>
            ))
          )}
        </div>
      </div>

      {/* Debug Info */}
      <div
        style={{
          marginTop: "20px",
          padding: "12px",
          backgroundColor: "#fef3c7",
          border: "1px solid #f59e0b",
          borderRadius: "6px",
          fontSize: "12px",
          color: "#92400e",
        }}
      >
        <strong>💡 Debug Info:</strong>
        <br />
        • Variables de entorno: VITE_SPEECH_API_URL, VITE_SPEECH_API_KEY
        <br />
        • El servicio incluye auto-configuración y manejo de errores
        <br />
        • Use "Auto Speak" para configuración automática de voz
        <br />
        • "Test Directo" bypasea el contexto y usa solo el servicio
        <br />• Los stats del servicio muestran configuración interna
      </div>

      {/* Hidden Audio Element */}
      <audio id="audio" style={{ display: "none" }} />
    </div>
  );
};
