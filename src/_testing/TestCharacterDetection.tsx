import { useState, useEffect } from "react";
import { useAIContext } from "../contexts/AIContext";

export const TestCharacterDetection = () => {
  const { generateResponse, isLoading, getStoredCharacter, resetSession } = useAIContext();
  const [response, setResponse] = useState<string>("");
  const [storedCharacter, setStoredCharacter] = useState<{
    character: string | null;
    timestamp: string | null;
  }>({ character: null, timestamp: null });
  const [error, setError] = useState<string>("");
  const [isWaitingForSpecific, setIsWaitingForSpecific] = useState<boolean>(false);

  // Función para verificar si el personaje es específico (copia de la lógica del servicio)
  const isCharacterSpecific = (character: string): boolean => {
    const genericTerms = [
      "alguien del mundo del entretenimiento",
      "una persona famosa",
      "un actor",
      "una actriz",
      "un personaje",
      "alguien famoso",
      "una celebridad",
      "un artista",
    ];

    const lowerCharacter = character.toLowerCase();
    return !genericTerms.some(term => lowerCharacter.includes(term));
  };

  // Actualizar personaje almacenado con manejo de errores
  const updateStoredCharacter = () => {
    try {
      if (typeof getStoredCharacter === 'function') {
        const stored = getStoredCharacter();
        setStoredCharacter(stored);
        setError(""); // Limpiar error si todo va bien
      } else {
        throw new Error("getStoredCharacter no está disponible");
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Error desconocido";
      console.error("❌ Error actualizando personaje almacenado:", err);
      setError(`Error: ${errorMessage}`);
      setStoredCharacter({ character: null, timestamp: null });
    }
  };

  useEffect(() => {
    try {
      updateStoredCharacter();

      // Escuchar eventos de actualización de personaje
      const handleCharacterUpdate = (event: CustomEvent) => {
        console.log("🎭 Personaje actualizado:", event.detail.character);
        setIsWaitingForSpecific(false);
        updateStoredCharacter();
      };

      window.addEventListener('characterUpdated', handleCharacterUpdate as EventListener);

      return () => {
        window.removeEventListener('characterUpdated', handleCharacterUpdate as EventListener);
      };
    } catch (err) {
      console.error("❌ Error en useEffect de TestCharacterDetection:", err);
      setError("Error inicializando componente");
    }
  }, []);

  const handleStartAuraGame = async () => {
    try {
      setError(""); // Limpiar errores previos
      setResponse(""); // Limpiar respuesta previa

      if (typeof generateResponse !== 'function') {
        throw new Error("generateResponse no está disponible");
      }

      const result = await generateResponse(
        "¡Hola! Estoy listo para adivinar lo que sea. ¿Ya tienes un personaje en mente?",
        "aura_piensa"
      );

      if (result && result.output) {
        setResponse(result.output);

        // Si el personaje detectado es genérico, mostrar que estamos esperando especificación
        if (result.characterName && !isCharacterSpecific(result.characterName)) {
          setIsWaitingForSpecific(true);
          console.log("⏳ Esperando personaje específico en 3 segundos...");
        }

        // Actualizar personaje almacenado después de la respuesta
        setTimeout(updateStoredCharacter, 100);

        console.log("🎭 Respuesta completa:", result);
      } else {
        throw new Error("Respuesta inválida de la IA");
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Error desconocido";
      console.error("❌ Error en handleStartAuraGame:", error);
      setError(`Error al generar respuesta: ${errorMessage}`);
      setResponse("");
    }
  };

  const handleResetSession = async () => {
    try {
      setError(""); // Limpiar errores previos

      if (typeof resetSession !== 'function') {
        throw new Error("resetSession no está disponible");
      }

      await resetSession();
      setResponse("");
      updateStoredCharacter();
      console.log("✅ Sesión reseteada");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Error desconocido";
      console.error("❌ Error al resetear:", error);
      setError(`Error al resetear sesión: ${errorMessage}`);
    }
  };

  const handleClearStorage = () => {
    try {
      setError(""); // Limpiar errores previos
      localStorage.removeItem("enygma_current_character");
      localStorage.removeItem("enygma_character_timestamp");
      updateStoredCharacter();
      console.log("🗑️ Storage limpiado manualmente");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Error desconocido";
      console.error("❌ Error al limpiar storage:", error);
      setError(`Error al limpiar storage: ${errorMessage}`);
    }
  };

  return (
    <div
      style={{
        position: "fixed",
        top: "20px",
        right: "20px",
        width: "400px",
        backgroundColor: "white",
        border: "2px solid #3b82f6",
        borderRadius: "12px",
        padding: "20px",
        boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
        zIndex: 9999,
        maxHeight: "80vh",
        overflowY: "auto",
      }}
    >
      <h3 style={{ margin: "0 0 16px 0", color: "#1e40af", fontSize: "18px" }}>
        🎭 Test Detección de Personajes
      </h3>

      {/* Mostrar errores si los hay */}
      {error && (
        <div
          style={{
            backgroundColor: "#fef2f2",
            border: "1px solid #dc2626",
            borderRadius: "8px",
            padding: "12px",
            marginBottom: "16px",
          }}
        >
          <h4 style={{ margin: "0 0 8px 0", color: "#dc2626", fontSize: "14px" }}>
            ❌ Error
          </h4>
          <div style={{ fontSize: "12px", color: "#7f1d1d" }}>
            {error}
          </div>
          <button
            onClick={() => setError("")}
            style={{
              marginTop: "8px",
              padding: "4px 8px",
              backgroundColor: "#dc2626",
              color: "white",
              border: "none",
              borderRadius: "4px",
              cursor: "pointer",
              fontSize: "11px",
            }}
          >
            Cerrar
          </button>
        </div>
      )}

      {/* Estado del personaje almacenado */}
      <div
        style={{
          backgroundColor: storedCharacter.character ? "#f0fdf4" : "#fef2f2",
          border: `1px solid ${storedCharacter.character ? "#16a34a" : "#dc2626"}`,
          borderRadius: "8px",
          padding: "12px",
          marginBottom: "16px",
        }}
      >
        <h4 style={{ margin: "0 0 8px 0", color: storedCharacter.character ? "#15803d" : "#dc2626" }}>
          📦 Personaje en LocalStorage
        </h4>
        <div style={{ fontSize: "14px", color: "#374151" }}>
          <div>
            <strong>Personaje:</strong> {storedCharacter.character || "Ninguno"}
          </div>
          {storedCharacter.timestamp && (
            <div>
              <strong>Guardado:</strong> {new Date(storedCharacter.timestamp).toLocaleString()}
            </div>
          )}
        </div>

        {/* Indicador de espera para personaje específico */}
        {isWaitingForSpecific && (
          <div style={{
            marginTop: "8px",
            padding: "8px",
            backgroundColor: "#fef3c7",
            border: "1px solid #f59e0b",
            borderRadius: "4px",
            fontSize: "12px",
            color: "#92400e"
          }}>
            ⏳ Esperando personaje específico en 3 segundos...
          </div>
        )}
      </div>

      {/* Controles */}
      <div style={{ display: "flex", flexDirection: "column", gap: "12px", marginBottom: "16px" }}>
        <button
          onClick={handleStartAuraGame}
          disabled={isLoading}
          style={{
            padding: "12px 16px",
            backgroundColor: isLoading ? "#9ca3af" : "#3b82f6",
            color: "white",
            border: "none",
            borderRadius: "8px",
            cursor: isLoading ? "not-allowed" : "pointer",
            fontSize: "14px",
            fontWeight: "500",
          }}
        >
          {isLoading ? "🤔 Pensando..." : "🎮 Iniciar Juego Aura"}
        </button>

        <div style={{ display: "flex", gap: "8px" }}>
          <button
            onClick={handleResetSession}
            disabled={isLoading}
            style={{
              flex: 1,
              padding: "8px 12px",
              backgroundColor: "#f59e0b",
              color: "white",
              border: "none",
              borderRadius: "6px",
              cursor: "pointer",
              fontSize: "12px",
            }}
          >
            🔄 Reset Sesión
          </button>

          <button
            onClick={handleClearStorage}
            style={{
              flex: 1,
              padding: "8px 12px",
              backgroundColor: "#dc2626",
              color: "white",
              border: "none",
              borderRadius: "6px",
              cursor: "pointer",
              fontSize: "12px",
            }}
          >
            🗑️ Limpiar Storage
          </button>
        </div>

        <button
          onClick={updateStoredCharacter}
          style={{
            padding: "8px 12px",
            backgroundColor: "#16a34a",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: "pointer",
            fontSize: "12px",
          }}
        >
          🔄 Actualizar Estado
        </button>
      </div>

      {/* Respuesta de la IA */}
      {response && (
        <div
          style={{
            backgroundColor: "#f8fafc",
            border: "1px solid #e2e8f0",
            borderRadius: "8px",
            padding: "12px",
            marginBottom: "12px",
          }}
        >
          <h4 style={{ margin: "0 0 8px 0", color: "#475569", fontSize: "14px" }}>
            🤖 Respuesta de la IA:
          </h4>
          <div style={{ fontSize: "13px", color: "#334155", lineHeight: "1.4" }}>
            {response}
          </div>
        </div>
      )}

      {/* Instrucciones */}
      <div
        style={{
          backgroundColor: "#fef3c7",
          border: "1px solid #f59e0b",
          borderRadius: "8px",
          padding: "12px",
        }}
      >
        <h4 style={{ margin: "0 0 8px 0", color: "#92400e", fontSize: "14px" }}>
          💡 Instrucciones:
        </h4>
        <ul style={{ margin: 0, paddingLeft: "16px", fontSize: "12px", color: "#78350f" }}>
          <li>Asegúrate de haber modificado el prompt para que la IA revele el personaje</li>
          <li>Haz clic en "Iniciar Juego Aura" para probar</li>
          <li>El personaje detectado se guardará automáticamente en localStorage</li>
          <li>Revisa la consola para logs detallados</li>
        </ul>
      </div>
    </div>
  );
};
