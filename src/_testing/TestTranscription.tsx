import React, { useState, useEffect } from "react";
import { useAudioContext } from "../contexts/AudioContext";
import { transcriptionService } from "../services/TranscriptionService";

export const TestTranscription: React.FC = () => {
  const {
    input: { transcription, isListening },
    startListening,
    stopListening,
    simulateTranscription,
    clearTranscription,
    reset,
    setSucwTimeout,
    closeWebView,
    hideAura,
    speakAura,
    sendAura,
    getId,
    isMHCAvailable,
    getStatus,
    normalize,
  } = useAudioContext();

  const [testText, setTestText] = useState<string>("Hola mundo");
  const [normalizedResult, setNormalizedResult] = useState<string>("");
  const [serviceStatus, setServiceStatus] = useState<any>({});
  const [mhcId, setMhcId] = useState<string>("");

  // ========== EFECTOS ==========
  useEffect(() => {
    // Obtener ID del MHC al cargar
    const id = getId();
    setMhcId(id);

    // Actualizar estado del servicio
    updateServiceStatus();
  }, [getId]);

  useEffect(() => {
    // Actualizar estado cuando cambie isListening
    updateServiceStatus();
  }, [isListening]);

  // ========== FUNCIONES ==========
  const updateServiceStatus = () => {
    const status = getStatus();
    setServiceStatus(status);
  };

  const handleNormalizeTest = async () => {
    try {
      const result = await normalize(testText);
      setNormalizedResult(result);
    } catch (error) {
      console.error("Error en normalización:", error);
      setNormalizedResult("Error");
    }
  };

  const handleDirectServiceTest = async () => {
    try {
      console.log("🧪 Test directo del servicio...");
      const result = await transcriptionService.testNormalization(testText);
      setNormalizedResult(result);
      console.log("Resultado directo:", result);
    } catch (error) {
      console.error("Error en test directo:", error);
    }
  };

  const handleMHCTest = () => {
    try {
      speakAura("Esto es una prueba de MHC");
    } catch (error) {
      console.error("Error en test MHC:", error);
    }
  };

  const predefinedTests = [
    "sí",
    "no",
    "no lo sé",
    "¿Es una persona real?",
    "¿Aparece en películas?",
    "salir",
    "cerrar",
    "Hola, ¿cómo estás?",
    "María José",
    "Ñoño",
  ];

  return (
    <div
      style={{
        padding: "20px",
        margin: "20px",
        border: "2px solid #e5e7eb",
        borderRadius: "12px",
        backgroundColor: "#f9fafb",
        fontFamily: "system-ui, sans-serif",
      }}
    >
      <h2
        style={{
          color: "#1f2937",
          marginBottom: "20px",
          fontSize: "24px",
          fontWeight: "bold",
        }}
      >
        🎤 Test Transcription Service
      </h2>

      {/* Estado del Servicio */}
      <div
        style={{
          backgroundColor: isListening ? "#dcfce7" : "#fef2f2",
          border: `1px solid ${isListening ? "#16a34a" : "#dc2626"}`,
          borderRadius: "8px",
          padding: "12px",
          marginBottom: "20px",
        }}
      >
        <h3
          style={{
            margin: "0 0 8px 0",
            color: isListening ? "#15803d" : "#dc2626",
          }}
        >
          {isListening ? "🎤 Escuchando" : "🔇 No Escuchando"}
        </h3>
        <div style={{ fontSize: "12px", color: "#6b7280" }}>
          <div>
            <strong>MHC Disponible:</strong> {isMHCAvailable() ? "✅" : "❌"}
          </div>
          <div>
            <strong>MHC ID:</strong> {mhcId}
          </div>
          <div>
            <strong>Transcripción Actual:</strong> {transcription || "Ninguna"}
          </div>
        </div>
      </div>

      {/* Controles de Escucha */}
      <div
        style={{
          display: "grid",
          gridTemplateColumns: "repeat(auto-fit, minmax(150px, 1fr))",
          gap: "10px",
          marginBottom: "20px",
        }}
      >
        <button
          onClick={startListening}
          disabled={isListening}
          style={{
            padding: "10px 16px",
            backgroundColor: isListening ? "#9ca3af" : "#16a34a",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: isListening ? "not-allowed" : "pointer",
            fontSize: "14px",
            fontWeight: "500",
          }}
        >
          🎤 Iniciar Escucha
        </button>

        <button
          onClick={stopListening}
          disabled={!isListening}
          style={{
            padding: "10px 16px",
            backgroundColor: !isListening ? "#9ca3af" : "#dc2626",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: !isListening ? "not-allowed" : "pointer",
            fontSize: "14px",
            fontWeight: "500",
          }}
        >
          🔇 Detener Escucha
        </button>

        <button
          onClick={clearTranscription}
          style={{
            padding: "10px 16px",
            backgroundColor: "#f59e0b",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: "pointer",
            fontSize: "14px",
            fontWeight: "500",
          }}
        >
          🧹 Limpiar
        </button>

        <button
          onClick={reset}
          style={{
            padding: "10px 16px",
            backgroundColor: "#6b7280",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: "pointer",
            fontSize: "14px",
            fontWeight: "500",
          }}
        >
          🔄 Reset
        </button>
      </div>

      {/* Test de Simulación */}
      <div
        style={{
          backgroundColor: "#eff6ff",
          border: "1px solid #3b82f6",
          borderRadius: "8px",
          padding: "16px",
          marginBottom: "20px",
        }}
      >
        <h4 style={{ margin: "0 0 12px 0", color: "#1e40af" }}>
          🧪 Simulación de Transcripción
        </h4>

        <div style={{ marginBottom: "12px" }}>
          <input
            type="text"
            value={testText}
            onChange={(e) => setTestText(e.target.value)}
            placeholder="Texto para simular..."
            style={{
              width: "100%",
              padding: "8px 12px",
              border: "1px solid #d1d5db",
              borderRadius: "6px",
              fontSize: "14px",
            }}
          />
        </div>

        <div
          style={{
            display: "flex",
            gap: "10px",
            flexWrap: "wrap",
            marginBottom: "12px",
          }}
        >
          <button
            onClick={() => simulateTranscription(testText)}
            style={{
              padding: "8px 16px",
              backgroundColor: "#3b82f6",
              color: "white",
              border: "none",
              borderRadius: "6px",
              cursor: "pointer",
              fontSize: "12px",
            }}
          >
            📤 Simular (Context)
          </button>

          <button
            onClick={handleDirectServiceTest}
            style={{
              padding: "8px 16px",
              backgroundColor: "#8b5cf6",
              color: "white",
              border: "none",
              borderRadius: "6px",
              cursor: "pointer",
              fontSize: "12px",
            }}
          >
            🔧 Test Directo (Service)
          </button>

          <button
            onClick={handleNormalizeTest}
            style={{
              padding: "8px 16px",
              backgroundColor: "#10b981",
              color: "white",
              border: "none",
              borderRadius: "6px",
              cursor: "pointer",
              fontSize: "12px",
            }}
          >
            ✨ Test Normalización
          </button>
        </div>

        {normalizedResult && (
          <div
            style={{
              backgroundColor: "#f0f9ff",
              border: "1px solid #0ea5e9",
              borderRadius: "4px",
              padding: "8px",
              fontSize: "14px",
              color: "#0369a1",
            }}
          >
            <strong>Normalizado:</strong> "{normalizedResult}"
          </div>
        )}
      </div>

      {/* Tests Predefinidos */}
      <div
        style={{
          backgroundColor: "#f0fdf4",
          border: "1px solid #16a34a",
          borderRadius: "8px",
          padding: "16px",
          marginBottom: "20px",
        }}
      >
        <h4 style={{ margin: "0 0 12px 0", color: "#15803d" }}>
          📋 Tests Predefinidos
        </h4>

        <div
          style={{
            display: "grid",
            gridTemplateColumns: "repeat(auto-fit, minmax(120px, 1fr))",
            gap: "8px",
          }}
        >
          {predefinedTests.map((test, index) => (
            <button
              key={index}
              onClick={() => simulateTranscription(test)}
              style={{
                padding: "6px 12px",
                backgroundColor: "#16a34a",
                color: "white",
                border: "none",
                borderRadius: "4px",
                cursor: "pointer",
                fontSize: "11px",
                fontWeight: "500",
              }}
            >
              "{test}"
            </button>
          ))}
        </div>
      </div>

      {/* MHC Controls */}
      <div
        style={{
          backgroundColor: "#fefce8",
          border: "1px solid #f59e0b",
          borderRadius: "8px",
          padding: "16px",
          marginBottom: "20px",
        }}
      >
        <h4 style={{ margin: "0 0 12px 0", color: "#92400e" }}>
          📱 Controles MHC
        </h4>

        <div
          style={{
            display: "flex",
            gap: "10px",
            flexWrap: "wrap",
            marginBottom: "12px",
          }}
        >
          <button
            onClick={handleMHCTest}
            disabled={!isMHCAvailable()}
            style={{
              padding: "8px 16px",
              backgroundColor: !isMHCAvailable() ? "#9ca3af" : "#f59e0b",
              color: "white",
              border: "none",
              borderRadius: "6px",
              cursor: !isMHCAvailable() ? "not-allowed" : "pointer",
              fontSize: "12px",
            }}
          >
            🗣️ Test Speak
          </button>

          <button
            onClick={() => sendAura("Test message")}
            disabled={!isMHCAvailable()}
            style={{
              padding: "8px 16px",
              backgroundColor: !isMHCAvailable() ? "#9ca3af" : "#0ea5e9",
              color: "white",
              border: "none",
              borderRadius: "6px",
              cursor: !isMHCAvailable() ? "not-allowed" : "pointer",
              fontSize: "12px",
            }}
          >
            📤 Send Aura
          </button>

          <button
            onClick={hideAura}
            disabled={!isMHCAvailable()}
            style={{
              padding: "8px 16px",
              backgroundColor: !isMHCAvailable() ? "#9ca3af" : "#dc2626",
              color: "white",
              border: "none",
              borderRadius: "6px",
              cursor: !isMHCAvailable() ? "not-allowed" : "pointer",
              fontSize: "12px",
            }}
          >
            👻 Hide Aura
          </button>

          <button
            onClick={() => setSucwTimeout(5000)}
            disabled={!isMHCAvailable()}
            style={{
              padding: "8px 16px",
              backgroundColor: !isMHCAvailable() ? "#9ca3af" : "#8b5cf6",
              color: "white",
              border: "none",
              borderRadius: "6px",
              cursor: !isMHCAvailable() ? "not-allowed" : "pointer",
              fontSize: "12px",
            }}
          >
            ⏱️ Set Timeout (5s)
          </button>
        </div>

        {!isMHCAvailable() && (
          <div
            style={{
              backgroundColor: "#fef2f2",
              border: "1px solid #dc2626",
              borderRadius: "4px",
              padding: "8px",
              fontSize: "12px",
              color: "#dc2626",
            }}
          >
            ⚠️ MHC no está disponible. Los controles están deshabilitados.
          </div>
        )}
      </div>

      {/* Resultado de Transcripción */}
      {transcription && (
        <div
          style={{
            backgroundColor: "#f0f9ff",
            border: "1px solid #0ea5e9",
            borderRadius: "8px",
            padding: "16px",
            marginBottom: "20px",
          }}
        >
          <h4 style={{ margin: "0 0 8px 0", color: "#0369a1" }}>
            📝 Última Transcripción:
          </h4>
          <div
            style={{
              backgroundColor: "white",
              border: "1px solid #e2e8f0",
              borderRadius: "4px",
              padding: "12px",
              fontSize: "16px",
              fontWeight: "500",
              color: "#1f2937",
            }}
          >
            "{transcription}"
          </div>
        </div>
      )}

      {/* Estado del Servicio */}
      <details style={{ marginBottom: "20px" }}>
        <summary
          style={{
            cursor: "pointer",
            fontWeight: "600",
            color: "#374151",
            padding: "10px",
            backgroundColor: "#f3f4f6",
            borderRadius: "6px",
          }}
        >
          ⚙️ Estado del Servicio
        </summary>
        <div
          style={{
            backgroundColor: "#f8fafc",
            border: "1px solid #e2e8f0",
            borderRadius: "6px",
            padding: "16px",
            marginTop: "8px",
          }}
        >
          <pre
            style={{
              fontSize: "12px",
              color: "#475569",
              margin: 0,
              whiteSpace: "pre-wrap",
            }}
          >
            {JSON.stringify(serviceStatus, null, 2)}
          </pre>
        </div>
      </details>

      {/* Comandos de Testing Global */}
      <div
        style={{
          backgroundColor: "#fef3c7",
          border: "1px solid #f59e0b",
          borderRadius: "8px",
          padding: "16px",
        }}
      >
        <h4 style={{ margin: "0 0 12px 0", color: "#92400e" }}>
          💻 Testing desde Consola
        </h4>
        <div style={{ fontSize: "13px", color: "#92400e", lineHeight: "1.5" }}>
          <div>
            <strong>Funciones disponibles en window:</strong>
          </div>
          <div>
            • <code>testTranscription("texto")</code> - Simular transcripción
          </div>
          <div>
            • <code>transcriptionService</code> - Acceso directo al servicio
          </div>
          <div>
            • <code>transcriptionService.getStatus()</code> - Estado del
            servicio
          </div>
          <div>
            • <code>transcriptionService.normalize("Téxto cón acentós")</code> -
            Test normalización
          </div>
        </div>
      </div>

      {/* Debug Info */}
      <div
        style={{
          marginTop: "20px",
          padding: "12px",
          backgroundColor: "#f1f5f9",
          border: "1px solid #64748b",
          borderRadius: "6px",
          fontSize: "12px",
          color: "#475569",
        }}
      >
        <strong>💡 Debug Info:</strong>
        <br />
        • El servicio maneja eventos de transcripción automáticamente
        <br />
        • Comandos de cierre: "salir", "cerrar", "exit", "close"
        <br />
        • La normalización quita acentos y convierte a minúsculas
        <br />
        • MHC se conecta automáticamente si está disponible
        <br />• Los listeners se configuran automáticamente al inicializar
      </div>
    </div>
  );
};
