import { useState } from "react";
import { useSpeechOutput } from "../contexts/SpeechOutputContext";

export const TestAudioAutoplay = () => {
  const { speakGameMessage, configure, state } = useSpeechOutput();
  const [testResult, setTestResult] = useState<string>("");
  const [isTestingAudio, setIsTestingAudio] = useState(false);

  const handleTestBasicAudio = async () => {
    setIsTestingAudio(true);
    setTestResult("");
    
    try {
      console.log("🧪 Iniciando test de audio básico...");
      
      // Configurar voz si no está lista
      if (!state.isReady) {
        console.log("🔧 Configurando voz...");
        await configure("female");
      }
      
      // Intentar hablar
      await speakGameMessage("Hola, este es un test de audio", "system");
      
      setTestResult("✅ Audio reproducido exitosamente");
      console.log("✅ Test de audio completado");
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Error desconocido";
      setTestResult(`❌ Error: ${errorMessage}`);
      console.error("❌ Error en test de audio:", error);
    } finally {
      setIsTestingAudio(false);
    }
  };

  const handleTestCharacterDetection = async () => {
    setIsTestingAudio(true);
    setTestResult("");
    
    try {
      console.log("🧪 Iniciando test de detección de personajes con audio...");
      
      // Configurar voz si no está lista
      if (!state.isReady) {
        console.log("🔧 Configurando voz...");
        await configure("female");
      }
      
      // Simular respuesta de la IA con personaje
      const testResponse = "¡Perfecto! Ya he pensado en alguien del mundo del entretenimiento. Puedes empezar a hacerme preguntas y yo responderé solo con 'Sí', 'No' o 'No lo sé'. ¡A ver si puedes adivinarlo! Personaje: Leonardo DiCaprio";
      
      await speakGameMessage(testResponse, "aura");
      
      // Verificar si se guardó el personaje
      const storedCharacter = localStorage.getItem("enygma_current_character");
      
      if (storedCharacter) {
        setTestResult(`✅ Audio y detección exitosos. Personaje: ${storedCharacter}`);
      } else {
        setTestResult("⚠️ Audio OK, pero no se detectó personaje");
      }
      
      console.log("✅ Test completado");
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Error desconocido";
      setTestResult(`❌ Error: ${errorMessage}`);
      console.error("❌ Error en test:", error);
    } finally {
      setIsTestingAudio(false);
    }
  };

  const handleClearStorage = () => {
    localStorage.removeItem("enygma_current_character");
    localStorage.removeItem("enygma_character_timestamp");
    localStorage.removeItem("enygma_audio_activated");
    setTestResult("🗑️ Storage limpiado");
    console.log("🗑️ Storage limpiado");
  };

  const getStoredCharacter = () => {
    return localStorage.getItem("enygma_current_character");
  };

  const getAudioActivated = () => {
    return localStorage.getItem("enygma_audio_activated");
  };

  return (
    <div
      style={{
        position: "fixed",
        bottom: "20px",
        right: "20px",
        width: "350px",
        backgroundColor: "white",
        border: "2px solid #f59e0b",
        borderRadius: "12px",
        padding: "16px",
        boxShadow: "0 4px 12px rgba(0,0,0,0.15)",
        zIndex: 9998,
        maxHeight: "70vh",
        overflowY: "auto",
      }}
    >
      <h3 style={{ margin: "0 0 12px 0", color: "#f59e0b", fontSize: "16px" }}>
        🔊 Test Audio & Autoplay
      </h3>

      {/* Estado del sistema */}
      <div
        style={{
          backgroundColor: "#fef3c7",
          border: "1px solid #f59e0b",
          borderRadius: "6px",
          padding: "10px",
          marginBottom: "12px",
          fontSize: "12px",
        }}
      >
        <div><strong>Voz configurada:</strong> {state.isReady ? "✅ Sí" : "❌ No"}</div>
        <div><strong>Audio activado:</strong> {getAudioActivated() ? "✅ Sí" : "❌ No"}</div>
        <div><strong>Personaje guardado:</strong> {getStoredCharacter() || "Ninguno"}</div>
        <div><strong>Configurando:</strong> {state.isConfiguring ? "⏳ Sí" : "✅ No"}</div>
      </div>

      {/* Controles de test */}
      <div style={{ display: "flex", flexDirection: "column", gap: "8px", marginBottom: "12px" }}>
        <button
          onClick={handleTestBasicAudio}
          disabled={isTestingAudio}
          style={{
            padding: "10px 12px",
            backgroundColor: isTestingAudio ? "#9ca3af" : "#3b82f6",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: isTestingAudio ? "not-allowed" : "pointer",
            fontSize: "13px",
            fontWeight: "500",
          }}
        >
          {isTestingAudio ? "🔄 Probando..." : "🎵 Test Audio Básico"}
        </button>

        <button
          onClick={handleTestCharacterDetection}
          disabled={isTestingAudio}
          style={{
            padding: "10px 12px",
            backgroundColor: isTestingAudio ? "#9ca3af" : "#16a34a",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: isTestingAudio ? "not-allowed" : "pointer",
            fontSize: "13px",
            fontWeight: "500",
          }}
        >
          {isTestingAudio ? "🔄 Probando..." : "🎭 Test Audio + Personaje"}
        </button>

        <button
          onClick={handleClearStorage}
          disabled={isTestingAudio}
          style={{
            padding: "8px 12px",
            backgroundColor: "#dc2626",
            color: "white",
            border: "none",
            borderRadius: "6px",
            cursor: "pointer",
            fontSize: "12px",
          }}
        >
          🗑️ Limpiar Storage
        </button>
      </div>

      {/* Resultado del test */}
      {testResult && (
        <div
          style={{
            backgroundColor: testResult.includes("❌") ? "#fef2f2" : "#f0fdf4",
            border: `1px solid ${testResult.includes("❌") ? "#dc2626" : "#16a34a"}`,
            borderRadius: "6px",
            padding: "10px",
            fontSize: "12px",
            color: testResult.includes("❌") ? "#dc2626" : "#16a34a",
            fontWeight: "500",
          }}
        >
          {testResult}
        </div>
      )}

      {/* Instrucciones */}
      <div
        style={{
          backgroundColor: "#f0f9ff",
          border: "1px solid #3b82f6",
          borderRadius: "6px",
          padding: "10px",
          marginTop: "12px",
          fontSize: "11px",
          color: "#1e40af",
        }}
      >
        <strong>💡 Instrucciones:</strong>
        <ul style={{ margin: "4px 0 0 0", paddingLeft: "16px" }}>
          <li>Si aparece error de autoplay, haz clic en cualquier parte de la página primero</li>
          <li>El banner azul te ayudará a activar el audio</li>
          <li>Revisa la consola para logs detallados</li>
        </ul>
      </div>
    </div>
  );
};
