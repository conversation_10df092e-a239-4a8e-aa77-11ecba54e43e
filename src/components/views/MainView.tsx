import { useState, useEffect } from "react";

interface GameMode {
  id: string;
  enabled: boolean;
  image: string;
  mode: "yo_pienso" | "aura_piensa";
  buttonText: string;
  description: string;
}

interface MainViewProps {
  handleStartGame: (mode: "yo_pienso" | "aura_piensa") => Promise<void>;
  handleShowRules: () => void;
  handleShowLives: () => void;
  handleShowClues: () => void;
  isStartingGame: boolean;
  isReady: boolean;
}

const MainView: React.FC<MainViewProps> = ({
  handleStartGame,
  handleShowRules,
  handleShowLives,
  handleShowClues,
  isStartingGame,
  isReady,
}) => {
  const [gameModes, setGameModes] = useState<GameMode[]>([]);

  useEffect(() => {
    const loadGameModes = async () => {
      try {
        const response = await fetch('/game-modes.json');
        const data = await response.json();
        setGameModes(data.gameModes || []);
      } catch (error) {
        console.error('Error loading game modes:', error);
      }
    };

    loadGameModes();
  }, []);

  return (
    <div className="content">
      <div className="menu-left">
        <div onClick={handleShowRules} style={{ cursor: "pointer" }}>
          <img src="assets/game/book.png" alt="Book" className="book-image" />
          <div>Reglas</div>
        </div>
      </div>

      <div className="game">
        <div className="game-header">
          <h1>Enygma</h1>
          <p>¿Puedes adivinar el personaje que está pensando Enygma?</p>
        </div>

        <div className="game-mode">
          {gameModes
            .filter(mode => mode.enabled)
            .map((mode) => (
              <div key={mode.id} className="enygma-wrapper">
                <img
                  src={mode.image}
                  alt="Enygma"
                  className="enygma-image"
                />

                <button
                  onClick={() => handleStartGame(mode.mode)}
                  disabled={isStartingGame || !isReady}
                >
                  {isStartingGame
                    ? "Iniciando..."
                    : !isReady
                      ? "Preparando..."
                      : mode.buttonText}
                </button>
              </div>
            ))}
        </div>
      </div>

      <div className="menu-right">
        <div onClick={handleShowLives} style={{ cursor: "pointer" }}>
          <img
            src="assets/game/lives.png"
            alt="Vidas"
            className="lives-image"
          />
          <div>20</div>
        </div>

        <div onClick={handleShowClues} style={{ cursor: "pointer" }}>
          <img
            src="assets/game/clues.png"
            alt="Pistas"
            className="clues-image"
          />
          <div>Pistas</div>
        </div>

        <div>
          <img src="assets/game/exit.png" alt="Salir" className="exit-image" />
          <div>Salir</div>
        </div>
      </div>
    </div>
  );
};

export default MainView;
