import { useState, useEffect } from "react";
import "./RulesView.scss";

// ========== TYPES ==========
interface RulePage {
  id: number;
  title: string;
  image?: string;
}

interface RulesViewData {
  title: string;
  pages: RulePage[];
}

interface RulesViewProps {
  isOpen: boolean;
  onClose: () => void;
}

const RulesView: React.FC<RulesViewProps> = ({ isOpen, onClose }) => {
  // ========== STATE ==========
  const [rulesData, setRulesData] = useState<RulesViewData | null>(null);
  const [currentPage, setCurrentPage] = useState(0);
  const [isAnimating, setIsAnimating] = useState(false);
  const [animationDirection, setAnimationDirection] = useState<"next" | "prev">("next");

  // ========== EFFECTS ==========
  useEffect(() => {
    const loadRulesData = async () => {
      try {
        console.log("🔄 Cargando reglas del juego desde /game-rules.json");
        const response = await fetch("/game-rules.json");

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log("✅ Reglas cargadas exitosamente:", data);
        setRulesData(data);
      } catch (error) {
        console.error("❌ Error cargando reglas del juego:", error);
      }
    };

    if (isOpen) {
      loadRulesData();
      setCurrentPage(0);
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "unset";
    }

    return () => {
      document.body.style.overflow = "unset";
    };
  }, [isOpen]);

  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        onClose();
      }
    };

    document.addEventListener("keydown", handleEscape);
    return () => document.removeEventListener("keydown", handleEscape);
  }, [isOpen, onClose]);

  // ========== NAVIGATION HANDLERS ==========
  const handleNextPage = () => {
    if (!rulesData || currentPage >= rulesData.pages.length - 1 || isAnimating) return;

    setIsAnimating(true);
    setAnimationDirection("next");

    setTimeout(() => {
      setCurrentPage(prev => prev + 1);
      setIsAnimating(false);
    }, 300);
  };

  const handlePrevPage = () => {
    if (currentPage <= 0 || isAnimating) return;

    setIsAnimating(true);
    setAnimationDirection("prev");

    setTimeout(() => {
      setCurrentPage(prev => prev - 1);
      setIsAnimating(false);
    }, 300);
  };

  const handlePageIndicatorClick = (pageIndex: number) => {
    if (pageIndex === currentPage || isAnimating) return;

    setIsAnimating(true);
    setAnimationDirection(pageIndex > currentPage ? "next" : "prev");

    setTimeout(() => {
      setCurrentPage(pageIndex);
      setIsAnimating(false);
    }, 300);
  };

  // ========== RENDER GUARDS ==========
  if (!isOpen || !rulesData) return null;
  const currentPageData = rulesData.pages[currentPage];

  return (
    <div className="content rules-modal">
      <button
        className={`rules-nav-arrow prev ${currentPage === 0 ? 'disabled' : ''}`}
        onClick={handlePrevPage}
        disabled={currentPage === 0 || isAnimating}
        aria-label="Página anterior"
      >
        ‹
      </button>

      <button
        className={`rules-nav-arrow next ${currentPage >= rulesData.pages.length - 1 ? 'disabled' : ''}`}
        onClick={handleNextPage}
        disabled={currentPage >= rulesData.pages.length - 1 || isAnimating}
        aria-label="Página siguiente"
      >
        ›
      </button>

      <div
        className={`rules-page-container ${isAnimating ? `page-flip-${animationDirection}` : ''}`}
        onClick={(e) => e.stopPropagation()}
      >
        <div className="rules-container">

          <div className="rules-image-card">
            {currentPageData.image && (
              <img
                src={currentPageData.image}
                alt={currentPageData.title}
                onError={(e) => {
                  e.currentTarget.style.display = 'none';
                }}
              />
            )}
          </div>

          <div className="rules-text-card">
            <h3>{currentPageData.title}</h3>

            <div className="rules-page-indicators">
              {rulesData.pages.map((_, index) => (
                <button
                  key={index}
                  className={`rules-page-indicator ${index === currentPage ? 'active' : ''}`}
                  onClick={() => handlePageIndicatorClick(index)}
                  disabled={isAnimating}
                  aria-label={`Ir a página ${index + 1}`}
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RulesView;
