import React, { Component, ReactNode } from 'react';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  componentName?: string;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: React.ErrorInfo;
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error(`❌ Error en ${this.props.componentName || 'componente'}:`, error);
    console.error('Error info:', errorInfo);
    
    this.setState({
      error,
      errorInfo,
    });
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback;
      }

      return (
        <div
          style={{
            padding: '20px',
            backgroundColor: '#fef2f2',
            border: '1px solid #dc2626',
            borderRadius: '8px',
            margin: '10px',
            maxWidth: '400px',
          }}
        >
          <h3 style={{ color: '#dc2626', margin: '0 0 10px 0', fontSize: '16px' }}>
            ❌ Error en {this.props.componentName || 'Componente'}
          </h3>
          
          <details style={{ fontSize: '12px', color: '#7f1d1d' }}>
            <summary style={{ cursor: 'pointer', fontWeight: 'bold', marginBottom: '8px' }}>
              Ver detalles del error
            </summary>
            
            <div style={{ backgroundColor: '#fee2e2', padding: '8px', borderRadius: '4px', marginBottom: '8px' }}>
              <strong>Error:</strong> {this.state.error?.message}
            </div>
            
            {this.state.errorInfo && (
              <div style={{ backgroundColor: '#fee2e2', padding: '8px', borderRadius: '4px' }}>
                <strong>Stack:</strong>
                <pre style={{ fontSize: '10px', overflow: 'auto', maxHeight: '100px' }}>
                  {this.state.errorInfo.componentStack}
                </pre>
              </div>
            )}
          </details>
          
          <button
            onClick={() => this.setState({ hasError: false, error: undefined, errorInfo: undefined })}
            style={{
              padding: '6px 12px',
              backgroundColor: '#dc2626',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer',
              fontSize: '12px',
              marginTop: '10px',
            }}
          >
            🔄 Reintentar
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
