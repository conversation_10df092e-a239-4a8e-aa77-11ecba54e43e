import { useState, useEffect } from "react";

interface AudioPermissionBannerProps {
  onAudioEnabled?: () => void;
}

export const AudioPermissionBanner = ({ onAudioEnabled }: AudioPermissionBannerProps) => {
  const [showBanner, setShowBanner] = useState(false);
  const [userHasInteracted, setUserHasInteracted] = useState(false);

  useEffect(() => {
    // Verificar si ya se ha activado el audio
    const audioActivated = localStorage.getItem("enygma_audio_activated");
    if (audioActivated === "true") {
      setUserHasInteracted(true);
      return;
    }

    // Detectar primera interacción del usuario
    const events = ['click', 'touchstart', 'keydown'];
    
    const handleFirstInteraction = () => {
      setUserHasInteracted(true);
      setShowBanner(false);
      
      // Guardar que el usuario ya interactuó
      localStorage.setItem("enygma_audio_activated", "true");
      
      // Notificar al componente padre
      onAudioEnabled?.();
      
      // Remover listeners
      events.forEach(event => {
        document.removeEventListener(event, handleFirstInteraction);
      });
    };

    // Mostrar banner después de un breve delay
    const timer = setTimeout(() => {
      if (!userHasInteracted) {
        setShowBanner(true);
      }
    }, 2000);

    // Agregar listeners
    events.forEach(event => {
      document.addEventListener(event, handleFirstInteraction, { once: true });
    });

    return () => {
      clearTimeout(timer);
      events.forEach(event => {
        document.removeEventListener(event, handleFirstInteraction);
      });
    };
  }, [onAudioEnabled]);

  const handleEnableAudio = () => {
    setUserHasInteracted(true);
    setShowBanner(false);
    localStorage.setItem("enygma_audio_activated", "true");
    onAudioEnabled?.();
  };

  if (!showBanner || userHasInteracted) {
    return null;
  }

  return (
    <div
      style={{
        position: "fixed",
        top: "20px",
        left: "50%",
        transform: "translateX(-50%)",
        backgroundColor: "#1e40af",
        color: "white",
        padding: "12px 20px",
        borderRadius: "8px",
        boxShadow: "0 4px 12px rgba(0,0,0,0.3)",
        zIndex: 10000,
        display: "flex",
        alignItems: "center",
        gap: "12px",
        maxWidth: "90vw",
        animation: "slideDown 0.3s ease-out",
      }}
    >
      <div style={{ fontSize: "14px", fontWeight: "500" }}>
        🔊 Haz clic para activar el audio
      </div>
      <button
        onClick={handleEnableAudio}
        style={{
          backgroundColor: "white",
          color: "#1e40af",
          border: "none",
          padding: "6px 12px",
          borderRadius: "4px",
          fontSize: "12px",
          fontWeight: "600",
          cursor: "pointer",
        }}
      >
        Activar
      </button>
      
      <style>{`
        @keyframes slideDown {
          from {
            opacity: 0;
            transform: translateX(-50%) translateY(-20px);
          }
          to {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
          }
        }
      `}</style>
    </div>
  );
};
