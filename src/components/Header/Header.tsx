import IconGoBack from "../icons/GoBack/IconGoBack";
import IconHome from "../icons/Home/IconHome";
import IconMenu from "../icons/Menu/IconMenu";
import IconSoundOn from "../icons/SoundOn/IconSoundOn";
import "./Header.scss";

interface HeaderProps {
  currentView: string;
  onBackToMain?: () => void;
  onToggleSound?: () => void;
  onGoHome?: () => void;
  showBackButton?: boolean;
}

export const Header: React.FC<HeaderProps> = ({
  currentView,
  onBackToMain,
  onToggleSound,
  onGoHome,
  showBackButton = false,
}) => {
  const renderTitle = () => {
    switch (currentView) {
      case "play":
        return "Enygma";
      case "rules":
        return "Reglas";
      case "lives":
        return "Tus preguntas restantes";
      case "clues":
        return "Pistas descubiertas";
      default:
        return "";
    }
  }

  const handleToggleSound = () => {
    if (onToggleSound) {
      onToggleSound();
    }
  };

  const handleGoHome = () => {
    if (onGoHome) {
      onGoHome();
    }
  };

  return (
    <div className="header">
      <div className="header-left">
        {currentView === "main" && (
          <IconMenu />
        )}

        {showBackButton && currentView !== "main" && onBackToMain && (
          <div className="back-button" onClick={onBackToMain}>
            <IconGoBack />
          </div>
        )}
      </div>

      <div className="header-title">
        {renderTitle()}
      </div>

      <div className="header-right">
        <div className="sound-icon" onClick={handleToggleSound}>
          <IconSoundOn />
        </div>

        <div className="home-icon" onClick={handleGoHome}>
          <IconHome />
        </div>
      </div>
    </div>
  );
};
