import { useEffect, useState, useCallback } from "react";
// 🔧 CAMBIO: Usar los nuevos contextos en lugar del antiguo AIContext
import { useEnygmaGame } from "../../../contexts/EnygmaGameContext"; // ✅ Usar EnygmaGame en lugar de AIContext
import { useGameOrchestrator } from "../../../contexts/GameOrchestratorContext"; // ✅ Para orchestración del flujo
import { useSpeechOutput } from "../../../contexts/SpeechOutputContext"; // ✅ Para speech output
import type { GameStep } from "../GameStepsContainer";
import { Loader } from "../../Loader";
import type { GameMode } from "../../../services/AIService";
import { Header } from "../../Header/Header.tsx";
import RulesView from "../../views/RulesView.tsx";

interface InitGameStepProps {
  onStepChange: (step: GameStep, data?: any) => void;
}

// 🆕 Nuevo tipo para controlar las vistas
type ViewMode = "main" | "rules" | "play" | "lives" | "clues";

export const InitGameStep: React.FC<InitGameStepProps> = ({ onStepChange }) => {
  // 🔧 CAMBIO: Usar los nuevos contextos
  const { } = useEnygmaGame(); // ✅ En lugar de useAIContext
  const { startGameFlow, isReady, setupProgress } = useGameOrchestrator(); // ✅ Para inicialización y flujo
  const {
    speakGameMessage,
    state: { isReady: speechReady, isConfiguring }
  } = useSpeechOutput(); // ✅ Para speech

  const [hasPlayedWelcome, setHasPlayedWelcome] = useState<boolean>(false);
  const [isStartingGame, setIsStartingGame] = useState<boolean>(false);
  const [showAudioBanner, setShowAudioBanner] = useState<boolean>(false);

  // 🆕 Estado para controlar qué vista mostrar
  const [currentView, setCurrentView] = useState<ViewMode>("main");

  // Verificar si se necesita mostrar banner de audio
  useEffect(() => {
    const hasAudioActivated = localStorage.getItem("enygma_audio_activated");
    const hasConsent = localStorage.getItem("enygma_analytics_consent");

    // Solo mostrar banner propio si no hay sistema de cookies
    if (hasAudioActivated === "true") {
      setHasPlayedWelcome(true);
      setShowAudioBanner(false);
    } else if (!hasConsent) {
      // Banner de cookies se encargará del audio
      setShowAudioBanner(false);
    } else {
      // Mostrar banner de audio simple
      setShowAudioBanner(true);
    }
  }, []);

  // 🔧 MEJORADO: Usar speakGameMessage en lugar de speakWithAutoConfig
  const playAudio = useCallback(
    async (text: string) => {
      try {
        await speakGameMessage(text, "system");
      } catch (error) {
        console.error("❌ Error reproduciendo audio:", error);
        throw error;
      }
    },
    [speakGameMessage],
  );

  // ========== EFECTO PARA NARRACIÓN DE BIENVENIDA ==========
  useEffect(() => {
    const playWelcomeMessage = () => {
      if (hasPlayedWelcome || !isReady || !speechReady) {
        return;
      }

      console.log("🎵 Audio preparado - requiere interacción del usuario para reproducir");
    };

    const timer = setTimeout(playWelcomeMessage, 1000);
    return () => clearTimeout(timer);
  }, [hasPlayedWelcome, isReady, speechReady]);

  // Nueva función para activar audio con interacción del usuario
  const activateAudioWithWelcome = useCallback(async () => {
    try {
      await playAudio("Bienvenido a Enygma, ¿lo adivinas?");
      setHasPlayedWelcome(true);
      setShowAudioBanner(false);
      localStorage.setItem("enygma_audio_activated", "true");
    } catch (error) {
      console.error("❌ Error activando audio:", error);
    }
  }, [playAudio]);

  // 🔧 CAMBIO: Usar startGameFlow del orchestrator en lugar de generateResponse
  const handleStartGame = async (mode: GameMode) => {
    setIsStartingGame(true);

    try {
      // Activar audio con bienvenida si no se ha reproducido aún
      if (!hasPlayedWelcome) {
        await activateAudioWithWelcome();
      }

      // 🔧 CAMBIO: Usar startGameFlow en lugar de startGame + generateResponse
      await startGameFlow(mode);

      // Cambiar al step de juego
      onStepChange("playing", {
        mode,
        initialMessage: "Juego iniciado",
        timestamp: new Date(),
      });

    } catch (error) {
      console.error("Error al iniciar el juego:", error);
    } finally {
      setIsStartingGame(false);
    }
  };

  const handleToggleSound = () => {
    console.log("Toggle sound");
  };

  const handleGoHome = () => {
    console.log("Go to home");
  };

  // 🆕 Nuevas funciones para manejar las vistas
  const handleShowRules = () => {
    setCurrentView("rules");
  };

  // const handleShowSettings = () => {
  //   setCurrentView("settings");
  // };

  // const handleShowHelp = () => {
  //   setCurrentView("help");
  // };

  const handleBackToMain = () => {
    setCurrentView("main");
  };

  // 🔧 MEJORADO: Mostrar progreso de inicialización si no está listo
  if (!isReady && setupProgress < 100) {
    return <Loader text={`Inicializando aplicación... ${setupProgress}%`} />;
  }

  // 🆕 Renderizar vista de reglas
  if (currentView === "rules") {
    return (
      <div className="init-game">
        <Header
          currentView={currentView}
          onBackToMain={handleBackToMain}
          onToggleSound={handleToggleSound}
          onGoHome={handleGoHome}
          showBackButton={true}
        />

        <RulesView
          isOpen={true}
          onClose={handleBackToMain}
        />
      </div>
    );
  }

  // // 🆕 Renderizar vista de configuración
  // if (currentView === "settings") {
  //   return (
  //     <div className="init-game">
  //       <GameHeader
  //         currentView={currentView}
  //         onBackToMain={handleBackToMain}
  //         onToggleSound={handleToggleSound}
  //         onGoHome={handleGoHome}
  //         showBackButton={true}
  //       />
  //       <div className="settings-view">
  //         <div className="main-content">
  //           <div className="game-header">
  //             <h1>Configuración</h1>
  //             <p>Ajusta las opciones del juego</p>
  //           </div>
  //           <div className="settings-options">
  //             <div className="setting-item">
  //               <label>Volumen del audio</label>
  //               <input type="range" min="0" max="100" defaultValue="50" />
  //             </div>
  //             <div className="setting-item">
  //               <label>Velocidad de narración</label>
  //               <input type="range" min="0.5" max="2" step="0.1" defaultValue="1" />
  //             </div>
  //             <div className="setting-item">
  //               <label>Activar vibraciones</label>
  //               <input type="checkbox" defaultChecked />
  //             </div>
  //           </div>
  //         </div>
  //       </div>
  //     </div>
  //   );
  // }

  // 🆕 Vista principal (existente)
  return (
    <>
      {/* CSS Animations */}
      <style>{`
        @keyframes pulse {
          0% { transform: translateX(-50%) scale(1); }
          50% { transform: translateX(-50%) scale(1.05); }
          100% { transform: translateX(-50%) scale(1); }
        }

        @keyframes fadeIn {
          from { opacity: 0; transform: translateY(20px); }
          to { opacity: 1; transform: translateY(0); }
        }

        .init-game {
          animation: fadeIn 0.6s ease-out;
        }

        .view-transition {
          transition: all 0.3s ease-in-out;
        }
      `}</style>

      <div className="init-game">
        {/* Audio Activation Banner - Solo si es necesario */}
        {showAudioBanner && !hasPlayedWelcome && (
          <div
            style={{
              position: "fixed",
              top: "20px",
              left: "50%",
              transform: "translateX(-50%)",
              background: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
              color: "white",
              padding: "12px 24px",
              borderRadius: "25px",
              boxShadow: "0 4px 15px rgba(0,0,0,0.2)",
              zIndex: 1000,
              textAlign: "center",
              animation: "pulse 2s infinite",
              cursor: "pointer",
            }}
            onClick={activateAudioWithWelcome}
          >
            <div style={{ display: "flex", alignItems: "center", gap: "10px" }}>
              <span style={{ fontSize: "20px" }}>🔊</span>
              <div>
                <div style={{ fontWeight: "bold", fontSize: "14px" }}>
                  ¡Activa el Audio!
                </div>
                <div style={{ fontSize: "12px", opacity: 0.9 }}>
                  Haz click para escuchar la bienvenida
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Loading overlay */}
        {(isStartingGame || isConfiguring) && <Loader text="Iniciando juego..." />}

        {/* Game Header */}
        <Header
          currentView={currentView}
          onToggleSound={handleToggleSound}
          onGoHome={handleGoHome}
          showBackButton={false}
        />

        {/* Main Content */}
        <div className="main-content">
          <div className="game-header">
            <h1>Enygma</h1>
            <p>¿Puedes adivinar el personaje que está pensando Enygma?</p>
          </div>

          <div className="character-image">
            <div className="character-circle">
              <div className="character-placeholder">
                🎭
              </div>
            </div>
          </div>

          <button
            className="play-button"
            onClick={() => handleStartGame("aura_piensa")}
            disabled={isStartingGame || !isReady}
          >
            {isStartingGame ? "Iniciando..." : !isReady ? "Preparando..." : "Jugar"}
          </button>
        </div>

        {/* 🔧 MODIFICADO: Navigation Icons - ahora incluye múltiples opciones */}
        <div className="bottom-navigation">
          <div className="nav-icon" onClick={handleShowRules}>
            <div className="nav-emoji">📖</div>
            <span>Reglas</span>
          </div>
        </div>
      </div>
    </>
  );
};
