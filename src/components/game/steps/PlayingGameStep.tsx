import React, { useState, useEffect } from "react";
import { useAIContext, type GameMode } from "../../../contexts/AIContext";
import { useGameContext } from "../../../contexts/GameContext";
import { useAudioContext } from "../../../contexts/AudioContext";
import type { GameStep } from "../GameStepsContainer";

interface PlayingGameStepProps {
  onStepChange: (step: GameStep, data?: any) => void;
  onResetGame: () => void;
  gameData: {
    mode: GameMode;
    initialMessage: string;
    timestamp: Date;
  };
}

interface Message {
  text: string;
  isUser: boolean;
  timestamp: Date;
}

export const PlayingGameStep: React.FC<PlayingGameStepProps> = ({
  onStepChange,
  onResetGame,
  gameData,
}) => {
  // Contextos
  const { isLoading: aiLoading, generateResponse } = useAIContext();
  const {
    gameMode,
    questionCount,
    gameState,
    addMessage: addGameMessage,
    endGame,
  } = useGameContext();
  const {
    input: { transcription },
    output: { isLocutionActivated },
    speak,
  } = useAudioContext();

  const [localMessages, setLocalMessages] = useState<Message[]>([]);
  const [userInput, setUserInput] = useState<string>("");
  const [isVoiceMode, setIsVoiceMode] = useState<boolean>(false);
  const [pendingAudioMessage, setPendingAudioMessage] = useState<string | null>(
    null,
  );

  // ========== EFECTOS ==========
  useEffect(() => {
    // Agregar mensaje inicial del juego
    if (gameData?.initialMessage) {
      addLocalMessage(gameData.initialMessage, false);
    }
  }, [gameData]);

  useEffect(() => {
    if (transcription && isVoiceMode) {
      setUserInput(transcription);
    }
  }, [transcription, isVoiceMode]);

  useEffect(() => {
    if (isLocutionActivated && pendingAudioMessage) {
      speak(pendingAudioMessage)
        .then(() => setPendingAudioMessage(null))
        .catch(console.error);
    }
  }, [isLocutionActivated, pendingAudioMessage, speak]);

  // ========== FUNCIONES ==========
  const addLocalMessage = (text: string, isUser: boolean) => {
    setLocalMessages((prev) => [
      ...prev,
      {
        text,
        isUser,
        timestamp: new Date(),
      },
    ]);

    // También agregar al contexto del juego
    addGameMessage(text, isUser);

    // Reproducir audio para mensajes de Aura
    if (!isUser) {
      if (isLocutionActivated) {
        speak(text).catch(console.error);
      } else {
        setPendingAudioMessage(text);
      }
    }
  };

  const handleSendMessage = async () => {
    if (!userInput.trim() || aiLoading) return;

    const message = userInput.trim();
    setUserInput("");
    addLocalMessage(message, true);

    try {
      const response = await generateResponse(message, gameMode!);

      if (response.ok) {
        addLocalMessage(response.output, false);

        // Verificar condiciones de fin de juego
        const isGuessAttempt =
          response.output.toLowerCase().includes("creo que estás pensando") ||
          response.output.toLowerCase().includes("es correcto") ||
          response.output.toLowerCase().includes("¿es correcto?");

        const isCorrectGuess =
          (message.toLowerCase().includes("sí") ||
            message.toLowerCase().includes("si") ||
            message.toLowerCase().includes("correcto")) &&
          isGuessAttempt;

        const reachedLimit = questionCount >= 20;

        if (isCorrectGuess || reachedLimit) {
          // Extraer nombre del personaje
          let characterName = "";

          if (isCorrectGuess) {
            const characterMatch = response.output.match(
              /pensando en ([^.?!]+)/i,
            );
            if (characterMatch) {
              characterName = characterMatch[1].trim();
            }
          }

          // Finalizar juego y cambiar a pantalla de resultados
          setTimeout(() => {
            endGame();
            onStepChange("results", {
              ...gameData,
              characterName,
              isWin: isCorrectGuess,
              messages: [
                ...localMessages,
                { text: message, isUser: true, timestamp: new Date() },
              ],
              questionCount,
              finalMessage: response.output,
            });
          }, 2000);
        }
      } else {
        addLocalMessage(
          "Lo siento, hubo un error. ¿Puedes repetir tu respuesta?",
          false,
        );
      }
    } catch (error) {
      console.error("Error al enviar mensaje:", error);
      addLocalMessage(
        "Hubo un problema con la conexión. Inténtalo de nuevo.",
        false,
      );
    }
  };

  const handleVoiceToggle = () => {
    setIsVoiceMode(!isVoiceMode);
    if (!isVoiceMode) {
      addLocalMessage("🎤 Modo de voz activado. Habla tu respuesta.", false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  // const handleTestTranscription = (text: string) => {
  //   simulateTranscription(text);
  //   setUserInput(text);
  // };

  // Si el juego no está en estado 'playing', mostrar mensaje de error
  if (gameState !== "playing") {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h2 className="text-xl font-bold text-gray-800 mb-4">
            Error: El juego no está activo
          </h2>
          <button
            onClick={onResetGame}
            className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600"
          >
            Volver al inicio
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-screen bg-gray-100">
      <div className="bg-white shadow-sm border-b border-gray-200 p-4">
        <div className="flex items-center justify-between">
          <div>
            <p className="text-sm text-gray-600">
              {gameMode === "yo_pienso"
                ? "Aura adivina tu personaje"
                : "Adivina el personaje de Aura"}
              - Pregunta {questionCount}/20
            </p>
          </div>
          <button
            onClick={onResetGame}
            className="px-4 py-2 bg-gray-500 text-white rounded-lg hover:bg-gray-600 transition-colors"
          >
            Nuevo Juego
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {localMessages.map((message, index) => (
          <div
            key={index}
            className={`flex ${message.isUser ? "justify-end" : "justify-start"}`}
          >
            <div
              className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                message.isUser
                  ? "bg-blue-500 text-white"
                  : "bg-white text-gray-800 shadow-sm border border-gray-200"
              }`}
            >
              {message.text}
            </div>
          </div>
        ))}

        {aiLoading && (
          <div className="flex justify-start">
            <div className="bg-white text-gray-800 shadow-sm border border-gray-200 px-4 py-2 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"></div>
                  <div
                    className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                    style={{ animationDelay: "0.1s" }}
                  ></div>
                  <div
                    className="w-2 h-2 bg-gray-400 rounded-full animate-bounce"
                    style={{ animationDelay: "0.2s" }}
                  ></div>
                </div>
                <span className="text-sm text-gray-600">
                  Aura está pensando...
                </span>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="bg-white border-t border-gray-200 p-4">
        <div className="flex items-center space-x-2">
          <input
            type="text"
            value={userInput}
            onChange={(e) => setUserInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={
              gameMode === "yo_pienso"
                ? "Responde: sí, no o no lo sé"
                : "Haz tu pregunta..."
            }
            className="flex-1 px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
            disabled={aiLoading}
          />

          <button
            onClick={handleVoiceToggle}
            className={`p-2 rounded-lg transition-colors ${
              isVoiceMode
                ? "bg-blue-500 text-white"
                : "bg-gray-200 text-gray-600 hover:bg-gray-300"
            }`}
            disabled={aiLoading}
          >
            🎤
          </button>

          <button
            onClick={handleSendMessage}
            disabled={!userInput.trim() || aiLoading}
            className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
          >
            Enviar
          </button>
        </div>

        {/* {import.meta.env.MODE === "development" && (
          <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
            <h4 className="text-sm font-semibold text-yellow-800 mb-2">
              🧪 Herramientas de Desarrollo
            </h4>

            <div className="mb-3 p-2 bg-white border rounded">
              <p className="text-xs text-gray-600">
                🔊 <strong>Estado Voz:</strong> {isLocutionActivated ? "✅ Activada" : "❌ Desactivada"}
                {isConfiguring && " (⏳ Configurando...)"}
              </p>
              <button
                onClick={() => configVoice("female")}
                disabled={isConfiguring}
                className="mt-2 px-3 py-1 bg-blue-500 text-white rounded text-xs hover:bg-blue-600 disabled:bg-gray-400"
              >
                {isConfiguring ? "Configurando..." : "🔧 Configurar Voz"}
              </button>
            </div>

            <h5 className="text-xs font-semibold text-yellow-700 mb-2">
              Simulación de Transcripción:
            </h5>
            <div className="flex flex-wrap gap-2 mb-3">
              <button
                onClick={() => handleTestTranscription("sí")}
                className="px-3 py-1 bg-green-200 text-green-800 rounded text-sm hover:bg-green-300"
              >
                "sí"
              </button>
              <button
                onClick={() => handleTestTranscription("no")}
                className="px-3 py-1 bg-red-200 text-red-800 rounded text-sm hover:bg-red-300"
              >
                "no"
              </button>
              <button
                onClick={() => handleTestTranscription("no lo sé")}
                className="px-3 py-1 bg-yellow-200 text-yellow-800 rounded text-sm hover:bg-yellow-300"
              >
                "no lo sé"
              </button>
              <button
                onClick={() => handleTestTranscription("¿Es una persona real?")}
                className="px-3 py-1 bg-blue-200 text-blue-800 rounded text-sm hover:bg-blue-300"
              >
                "¿Es una persona real?"
              </button>
            </div>
          </div>
        )} */}

        {/* {isVoiceMode && (
          <div className="mt-2">
            <p className="text-xs text-gray-500">
              🎤 Modo de voz activado {import.meta.env.MODE === "development" ? "- Usa los botones de simulación arriba" : ""}
            </p>
          </div>
        )} */}
      </div>
    </div>
  );
};
