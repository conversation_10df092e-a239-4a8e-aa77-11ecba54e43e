import React, { useState, useEffect } from "react";
import { useMovistarPlusContext } from "../../../contexts/MovistarPlusContext";
import { useAudioContext } from "../../../contexts/AudioContext";
import type { GameStep } from "../GameStepsContainer";

interface ResultsGameStepProps {
  onStepChange: (step: GameStep, data?: any) => void;
  onResetGame: () => void;
  gameData: {
    mode: string;
    characterName: string;
    isWin: boolean;
    messages: Array<{ text: string; isUser: boolean; timestamp: Date }>;
    questionCount: number;
    finalMessage: string;
  };
}

export const ResultsGameStep: React.FC<ResultsGameStepProps> = ({
  onStepChange,
  onResetGame,
  gameData,
}) => {
  const { getMovistarPlusFilms, isLoading: movistarLoading } =
    useMovistarPlusContext();
  const { speakWithAutoConfig } = useAudioContext();

  const [movistarResults, setMovistarResults] = useState<any>(null);
  const [searchError, setSearchError] = useState<string | null>(null);
  const [hasNarratedResult, setHasNarratedResult] = useState<boolean>(false);

  // ========== EFECTOS ==========
  useEffect(() => {
    const handleGameResults = async () => {
      // Narrar resultado si hay personaje
      if (gameData.characterName && !hasNarratedResult) {
        try {
          const resultMessage = gameData.isWin
            ? `¡Excelente! Ahora te mostraré el contenido disponible de ${gameData.characterName} en Movistar Plus`
            : `El juego ha terminado. Te mostraré contenido relacionado en Movistar Plus`;

          await speakWithAutoConfig(resultMessage, "female");
          setHasNarratedResult(true);
        } catch (error) {
          console.error("Error narrando resultado:", error);
        }
      }

      // Buscar contenido en Movistar+ si hay personaje
      if (gameData.characterName && !movistarResults && !searchError) {
        try {
          const filmData = {
            title: gameData.characterName,
            recommendation: `Contenido relacionado con ${gameData.characterName}`,
          };

          const results = await getMovistarPlusFilms(filmData);
          setMovistarResults(results);
        } catch (error) {
          console.error("Error al buscar contenido:", error);
          setSearchError("No se pudo buscar el contenido en este momento.");
        }
      }
    };

    handleGameResults();
  }, [gameData, movistarResults, searchError, hasNarratedResult]);

  const handlePlayAgain = () => {
    onResetGame();
  };

  const getResultIcon = () => {
    if (!gameData.characterName) return "🤔";
    return gameData.isWin ? "🎉" : "🎯";
  };

  const getResultTitle = () => {
    if (!gameData.characterName) {
      return "Juego Terminado";
    }
    return gameData.isWin ? "¡Felicidades!" : "¡Buen intento!";
  };

  const getResultMessage = () => {
    if (!gameData.characterName) {
      return "El juego ha terminado sin resultado definido.";
    }

    if (gameData.isWin) {
      return `¡Aura adivinó correctamente! El personaje era ${gameData.characterName}.`;
    } else {
      return `Se agotaron las 20 preguntas. El personaje era ${gameData.characterName || "desconocido"}.`;
    }
  };

  return (
    <div className="results-game-step min-h-screen bg-gray-100 p-4">
      <div className="max-w-4xl mx-auto">
        {/* Header de Resultados */}
        <div className="bg-white rounded-lg shadow-lg p-6 mb-6 text-center">
          <div className="text-6xl mb-4">{getResultIcon()}</div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">
            {getResultTitle()}
          </h1>
          <p className="text-lg text-gray-600 mb-4">{getResultMessage()}</p>

          {/* Estadísticas del juego */}
          <div className="flex justify-center space-x-8 mb-6">
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-500">
                {gameData.questionCount}
              </div>
              <div className="text-sm text-gray-500">Preguntas</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-500">
                {gameData.mode === "yo_pienso" ? "Usuario" : "Aura"}
              </div>
              <div className="text-sm text-gray-500">Modo</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-purple-500">
                {gameData.isWin ? "Victoria" : "Empate"}
              </div>
              <div className="text-sm text-gray-500">Resultado</div>
            </div>
          </div>

          {/* Botón para jugar de nuevo */}
          <button
            onClick={handlePlayAgain}
            className="px-8 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors font-semibold"
          >
            🎮 Jugar de Nuevo
          </button>
        </div>

        {/* Sección de Movistar+ */}
        {gameData.characterName && (
          <div className="bg-white rounded-lg shadow-lg p-6">
            <h2 className="text-2xl font-bold text-gray-800 mb-4">
              📺 Contenido en Movistar+
            </h2>

            {movistarLoading && (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
                <span className="ml-4 text-gray-600">
                  Buscando contenido de {gameData.characterName}...
                </span>
              </div>
            )}

            {searchError && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center">
                  <span className="text-red-500 text-xl mr-2">⚠️</span>
                  <span className="text-red-700">{searchError}</span>
                </div>
              </div>
            )}

            {movistarResults && !movistarLoading && (
              <div className="space-y-4">
                <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-lg p-4">
                  <h3 className="text-lg font-semibold mb-2">
                    🎬 Resultados para "{gameData.characterName}"
                  </h3>
                  <p className="text-purple-100">
                    Contenido encontrado en la plataforma Movistar+
                  </p>
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="font-semibold text-gray-800 mb-3">
                    📊 Datos de la búsqueda:
                  </h4>
                  <div className="bg-white rounded border p-4 overflow-auto">
                    <pre className="text-sm text-gray-600 whitespace-pre-wrap">
                      {JSON.stringify(movistarResults, null, 2)}
                    </pre>
                  </div>
                </div>

                {/* Botón para nueva búsqueda */}
                <div className="text-center">
                  <button
                    onClick={() => {
                      setMovistarResults(null);
                      setSearchError(null);
                    }}
                    className="px-6 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors"
                  >
                    🔄 Buscar de Nuevo
                  </button>
                </div>
              </div>
            )}

            {!movistarResults && !movistarLoading && !searchError && (
              <div className="text-center py-8 text-gray-500">
                <div className="text-4xl mb-2">🎭</div>
                <p>Preparando búsqueda de contenido...</p>
              </div>
            )}
          </div>
        )}

        {/* Resumen del juego */}
        <div className="bg-white rounded-lg shadow-lg p-6 mt-6">
          <h2 className="text-2xl font-bold text-gray-800 mb-4">
            📝 Resumen del Juego
          </h2>

          <div className="space-y-3 max-h-64 overflow-y-auto">
            {gameData.messages.slice(-10).map((message, index) => (
              <div
                key={index}
                className={`flex ${message.isUser ? "justify-end" : "justify-start"}`}
              >
                <div
                  className={`max-w-md px-4 py-2 rounded-lg text-sm ${
                    message.isUser
                      ? "bg-blue-100 text-blue-800"
                      : "bg-gray-100 text-gray-800"
                  }`}
                >
                  <div className="font-semibold text-xs mb-1">
                    {message.isUser ? "Tú" : "Aura"}
                  </div>
                  {message.text}
                </div>
              </div>
            ))}
          </div>

          {gameData.messages.length > 10 && (
            <p className="text-center text-gray-500 text-sm mt-3">
              Mostrando las últimas 10 conversaciones de{" "}
              {gameData.messages.length} total
            </p>
          )}
        </div>

        {/* Footer con información adicional */}
        <div className="bg-gray-800 text-white rounded-lg p-6 mt-6 text-center">
          <h3 className="text-lg font-semibold mb-2">
            ¡Gracias por jugar a Enygma!
          </h3>
          <p className="text-gray-300 mb-4">
            ¿Te ha gustado el juego? ¡Prueba con otro personaje!
          </p>

          <div className="flex justify-center space-x-4">
            <button
              onClick={handlePlayAgain}
              className="px-6 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors"
            >
              🎯 Nuevo Personaje
            </button>
            <button
              onClick={() => onStepChange("init")}
              className="px-6 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
            >
              🏠 Pantalla Principal
            </button>
          </div>
        </div>

        {/* Debug info en desarrollo */}
        {import.meta.env.MODE === "development" && (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mt-6">
            <h4 className="text-sm font-semibold text-yellow-800 mb-2">
              🧪 Información de Debug
            </h4>
            <div className="text-xs text-yellow-700 space-y-1">
              <div>
                <strong>Modo:</strong> {gameData.mode}
              </div>
              <div>
                <strong>Personaje:</strong>{" "}
                {gameData.characterName || "No definido"}
              </div>
              <div>
                <strong>Victoria:</strong> {gameData.isWin ? "Sí" : "No"}
              </div>
              <div>
                <strong>Preguntas:</strong> {gameData.questionCount}/20
              </div>
              <div>
                <strong>Mensajes:</strong> {gameData.messages.length}
              </div>
              <div>
                <strong>Movistar+ Cargado:</strong>{" "}
                {movistarResults ? "Sí" : "No"}
              </div>
              <div>
                <strong>Narración:</strong>{" "}
                {hasNarratedResult ? "Completada" : "Pendiente"}
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
