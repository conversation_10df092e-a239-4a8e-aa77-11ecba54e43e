// ========== TYPES ==========
export type GameStep = "init" | "playing" | "results";

export interface GameStepProps {
  onStepChange: (step: GameStep) => void;
}

// ========== MAIN GAME CONTAINER ==========
import React, { useState } from "react";
import { InitGameStep } from "./steps/InitGameStep";
import { PlayingGameStep } from "./steps/PlayingGameStep";
import { ResultsGameStep } from "./steps/ResultsGameStep";

export const GameStepsContainer: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<GameStep>("init");
  const [gameData, setGameData] = useState<any>(null);

  const handleStepChange = (step: GameStep, data?: any) => {
    setCurrentStep(step);
    if (data) {
      setGameData(data);
    }
  };

  const handleResetGame = () => {
    setCurrentStep("init");
    setGameData(null);
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case "init":
        return <InitGameStep onStepChange={handleStepChange} />;

      case "playing":
        return (
          <PlayingGameStep
            onStepChange={handleStepChange}
            onResetGame={handleResetGame}
            gameData={gameData}
          />
        );

      case "results":
        return (
          <ResultsGameStep
            onStepChange={handleStepChange}
            onResetGame={handleResetGame}
            gameData={gameData}
          />
        );

      default:
        return <InitGameStep onStepChange={handleStepChange} />;
    }
  };

  return <div className="game-steps-container">{renderCurrentStep()}</div>;
};
