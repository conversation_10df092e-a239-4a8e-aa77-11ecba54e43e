.loader-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.8);
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.loader {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  font-family: "Arial", sans-serif;
  color: #333;
  font-size: 18px;
  text-align: center;
  animation: fadeIn 0.3s ease-in-out;
  padding: 20px;
  border-radius: 10px;
  box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #f0f0f0, #e0e0e0);
  border: 1px solid #ccc;
  box-sizing: border-box;
  width: 200px;
  max-width: 90%;
  animation: fadeIn 0.3s ease-in-out;
  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  .loader-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #ccc;
    border-top: 4px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    @keyframes spin {
      from {
        transform: rotate(0deg);
      }
      to {
        transform: rotate(360deg);
      }
    }
  }
  p {
    margin: 0;
    font-size: 16px;
    font-weight: 500;
    color: #555;
    animation: fadeIn 0.5s ease-in-out;
    @keyframes fadeIn {
      from {
        opacity: 0;
      }
      to {
        opacity: 1;
      }
    }
  }
  @media (prefers-color-scheme: dark) {
    background: linear-gradient(135deg, #333, #444);
    color: #f0f0f0;
    border: 1px solid #555;
    .loader-spinner {
      border: 4px solid #555;
      border-top: 4px solid #00bfff;
    }
    p {
      color: #ddd;
    }
  }
  @media (max-width: 600px) {
    width: 90%;
    font-size: 16px;
    .loader-spinner {
      width: 30px;
      height: 30px;
      border-width: 3px;
      animation: spin 0.8s linear infinite;
      @keyframes spin {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }
    }
    p {
      font-size: 14px;
      animation: fadeIn 0.4s ease-in-out;
      @keyframes fadeIn {
        from {
          opacity: 0;
        }
        to {
          opacity: 1;
        }
      }
    }
  }
  @media (max-width: 400px) {
    width: 95%;
    .loader-spinner {
      width: 25px;
      height: 25px;
      border-width: 2px;
      animation: spin 0.6s linear infinite;
      @keyframes spin {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }
    }
    p {
      font-size: 12px;
      animation: fadeIn 0.3s ease-in-out;
      @keyframes fadeIn {
        from {
          opacity: 0;
        }
        to {
          opacity: 1;
        }
      }
    }
  }
  @media (max-width: 320px) {
    width: 100%;
    .loader-spinner {
      width: 20px;
      height: 20px;
      border-width: 1px;
      animation: spin 0.5s linear infinite;
      @keyframes spin {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }
    }
    p {
      font-size: 10px;
      animation: fadeIn 0.2s ease-in-out;
      @keyframes fadeIn {
        from {
          opacity: 0;
        }
        to {
          opacity: 1;
        }
      }
    }
  }
}
