import { createRoot } from "react-dom/client";
import { StrictMode } from "react";
import App from "./App.tsx";
import { AppProvider } from "./contexts/AppContext.tsx";
import { AIProvider } from "./contexts/AIContext.tsx";
import { EnygmaGameProvider } from "./contexts/EnygmaGameContext.tsx";
import { GameOrchestratorProvider } from "./contexts/GameOrchestratorContext.tsx";
import { MovistarPlusProvider } from "./contexts/MovistarPlusContext.tsx";
import { SpeechInputProvider } from "./contexts/SpeechInputContext.tsx";
import { SpeechOutputProvider } from "./contexts/SpeechOutputContext.tsx";
import "./index.scss";
import { MHCProvider } from "./contexts/MHCContext.tsx";

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <AppProvider> {/* 1. AppProvider (base de la app) */}
      <AIProvider>              {/* 🤖 AI Provider para funcionalidades de IA */}
        <MHCProvider>            {/* 🔧 NUEVO: MHC Provider para funcionalidades de hardware */}
          <SpeechInputProvider>     {/* 🎤 Base: Solo transcripción */}
            <SpeechOutputProvider>  {/* 🔊 Base: Solo síntesis */}
              <EnygmaGameProvider>  {/* 🎮 Core: lógica del juego - usa speech services) */}
                <MovistarPlusProvider> {/* 📺 servicios externos - independiente */}
                  <GameOrchestratorProvider>  {/* 🎯 coordinador - usa TODOS los anteriores */}
                    <App />
                  </GameOrchestratorProvider>
                </MovistarPlusProvider>
              </EnygmaGameProvider>
            </SpeechOutputProvider>
          </SpeechInputProvider>
        </MHCProvider>
      </AIProvider>
    </AppProvider>
  </StrictMode>,
);
