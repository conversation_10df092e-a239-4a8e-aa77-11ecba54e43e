// contexts/MHCContext.tsx
import {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  type ReactNode,
} from "react";
import { transcriptionService, type IMHC } from "../services/TranscriptionService";
import { log } from "../services/LogService";

// ========== TYPES ==========
export interface MHCState {
  isAvailable: boolean;
  deviceId: string;
  connectionStatus: "disconnected" | "connecting" | "connected" | "error";
  capabilities: MHCCapabilities;
  errorMessage: string | null;
}

export interface MHCCapabilities {
  canSpeak: boolean;
  canCloseWebView: boolean;
  canSetTimeout: boolean;
  canHideAura: boolean;
  canSendMessages: boolean;
}

export interface MHCContextProps {
  // Current state
  state: MHCState;

  // Core MHC functionality
  setSucwTimeout: (timeMS: number) => void;
  closeWebView: () => void;
  hideAura: () => void;
  speakAura: (text: string) => void;
  sendAura: (text: string) => void;
  getId: () => string;

  // Enhanced MHC operations
  speakAndHide: (text: string, hideDelay?: number) => Promise<void>;
  sendSystemMessage: (message: string, type: "info" | "warning" | "error") => void;
  setAppTimeout: (seconds: number) => void;

  // Connection management
  checkConnection: () => Promise<boolean>;
  reconnect: () => Promise<boolean>;
  getDeviceInfo: () => object;

  // Game-specific MHC
  announceGameStart: (gameMode: string) => void;
  announceGameEnd: (result: "win" | "lose" | "draw") => void;
  showGameProgress: (current: number, total: number) => void;

  // Utilities
  isFeatureAvailable: (feature: keyof MHCCapabilities) => boolean;
  testAllFeatures: () => Promise<MHCCapabilities>;
  reset: () => void;
}

// ========== CONTEXT ==========
const MHCContext = createContext<MHCContextProps | undefined>(undefined);

export const useMHC = () => {
  const context = useContext(MHCContext);
  if (!context) {
    throw new Error("useMHC must be used within MHCProvider");
  }
  return context;
};

// ========== PROVIDER ==========
export const MHCProvider = ({ children }: { children: ReactNode }) => {
  // ========== LOCAL STATE ==========
  const [state, setState] = useState<MHCState>({
    isAvailable: false,
    deviceId: "",
    connectionStatus: "disconnected",
    capabilities: {
      canSpeak: false,
      canCloseWebView: false,
      canSetTimeout: false,
      canHideAura: false,
      canSendMessages: false,
    },
    errorMessage: null,
  });

  const [mhcInstance, setMhcInstance] = useState<IMHC | null>(null);

  // ========== EFFECTS ==========
  useEffect(() => {
    log.info("mhc", "📱 Inicializando MHCProvider");

    initializeMHC();

    return () => {
      log.info("mhc", "🧹 Limpiando MHCProvider");
    };
  }, []);

  // ========== INITIALIZATION ==========
  const initializeMHC = useCallback(async () => {
    setState(prev => ({ ...prev, connectionStatus: "connecting" }));

    try {
      // Obtener instancia MHC del servicio de transcripción
      const mhc = transcriptionService.getMHC();
      setMhcInstance(mhc);

      // Verificar disponibilidad
      const isAvailable = transcriptionService.isMHCAvailable();

      if (isAvailable) {
        const deviceId = mhc.getId();
        const capabilities = await testAllFeatures();

        setState(prev => ({
          ...prev,
          isAvailable: true,
          deviceId,
          connectionStatus: "connected",
          capabilities,
          errorMessage: null,
        }));

        log.success("mhc", "✅ MHC inicializado correctamente", { deviceId, capabilities });
      } else {
        setState(prev => ({
          ...prev,
          isAvailable: false,
          connectionStatus: "disconnected",
          errorMessage: "MHC no disponible en este dispositivo",
        }));

        log.warn("mhc", "⚠️ MHC no disponible");
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Error inicializando MHC";

      setState(prev => ({
        ...prev,
        connectionStatus: "error",
        errorMessage,
      }));

      log.error("mhc", "❌ Error inicializando MHC", error);
    }
  }, []);

  // ========== CORE MHC FUNCTIONALITY ==========
  const setSucwTimeout = useCallback((timeMS: number) => {
    if (!mhcInstance || !state.isAvailable) {
      log.warn("mhc", "⚠️ MHC no disponible para setSucwTimeout");
      return;
    }

    try {
      mhcInstance.setSucwTimeout(timeMS);
      log.info("mhc", `⏰ Timeout establecido: ${timeMS}ms`);
    } catch (error) {
      log.error("mhc", "❌ Error estableciendo timeout", error);
    }
  }, [mhcInstance, state.isAvailable]);

  const closeWebView = useCallback(() => {
    if (!mhcInstance || !state.isAvailable) {
      log.warn("mhc", "⚠️ MHC no disponible para closeWebView");
      return;
    }

    try {
      mhcInstance.closeWebView();
      log.info("mhc", "🚪 WebView cerrado");
    } catch (error) {
      log.error("mhc", "❌ Error cerrando WebView", error);
    }
  }, [mhcInstance, state.isAvailable]);

  const hideAura = useCallback(() => {
    if (!mhcInstance || !state.isAvailable) {
      log.warn("mhc", "⚠️ MHC no disponible para hideAura");
      return;
    }

    try {
      mhcInstance.hideAura();
      log.info("mhc", "👻 Aura ocultado");
    } catch (error) {
      log.error("mhc", "❌ Error ocultando Aura", error);
    }
  }, [mhcInstance, state.isAvailable]);

  const speakAura = useCallback((text: string) => {
    if (!mhcInstance || !state.isAvailable) {
      log.warn("mhc", "⚠️ MHC no disponible para speakAura");
      return;
    }

    try {
      mhcInstance.speakAura(text);
      log.info("mhc", `🗣️ Aura hablando: "${text.substring(0, 50)}..."`);
    } catch (error) {
      log.error("mhc", "❌ Error haciendo hablar a Aura", error);
    }
  }, [mhcInstance, state.isAvailable]);

  const sendAura = useCallback((text: string) => {
    if (!mhcInstance || !state.isAvailable) {
      log.warn("mhc", "⚠️ MHC no disponible para sendAura");
      return;
    }

    try {
      mhcInstance.sendAura(text);
      log.info("mhc", `📤 Mensaje enviado a Aura: "${text.substring(0, 50)}..."`);
    } catch (error) {
      log.error("mhc", "❌ Error enviando mensaje a Aura", error);
    }
  }, [mhcInstance, state.isAvailable]);

  const getId = useCallback((): string => {
    if (!mhcInstance) {
      log.warn("mhc", "⚠️ MHC no disponible para getId, usando fallback");
      return `fallback-${Date.now()}`;
    }

    try {
      const id = mhcInstance.getId();
      log.debug("mhc", `🆔 Device ID: ${id}`);
      return id;
    } catch (error) {
      log.error("mhc", "❌ Error obteniendo ID", error);
      return `error-${Date.now()}`;
    }
  }, [mhcInstance]);

  // ========== ENHANCED MHC OPERATIONS ==========
  const speakAndHide = useCallback(async (text: string, hideDelay: number = 2000): Promise<void> => {
    if (!state.capabilities.canSpeak || !state.capabilities.canHideAura) {
      log.warn("mhc", "⚠️ Funcionalidades requeridas no disponibles para speakAndHide");
      return;
    }

    try {
      log.info("mhc", `🗣️👻 Hablar y ocultar: "${text.substring(0, 50)}..."`);

      speakAura(text);

      // Esperar y luego ocultar
      setTimeout(() => {
        hideAura();
      }, hideDelay);

    } catch (error) {
      log.error("mhc", "❌ Error en speakAndHide", error);
    }
  }, [state.capabilities, speakAura, hideAura]);

  const sendSystemMessage = useCallback((message: string, type: "info" | "warning" | "error") => {
    const prefixes = {
      info: "ℹ️",
      warning: "⚠️",
      error: "❌"
    };

    const systemMessage = `${prefixes[type]} ${message}`;
    sendAura(systemMessage);
  }, [sendAura]);

  const setAppTimeout = useCallback((seconds: number) => {
    const timeMS = seconds * 1000;
    setSucwTimeout(timeMS);
    log.info("mhc", `⏱️ Timeout de aplicación establecido: ${seconds}s`);
  }, [setSucwTimeout]);

  // ========== CONNECTION MANAGEMENT ==========
  const checkConnection = useCallback(async (): Promise<boolean> => {
    try {
      if (!mhcInstance) return false;

      // Test simple: intentar obtener ID
      const id = mhcInstance.getId();
      const isConnected = Boolean(id);

      setState(prev => ({
        ...prev,
        connectionStatus: isConnected ? "connected" : "disconnected"
      }));

      return isConnected;
    } catch (error) {
      setState(prev => ({
        ...prev,
        connectionStatus: "error",
        errorMessage: "Error verificando conexión MHC"
      }));
      return false;
    }
  }, [mhcInstance]);

  const reconnect = useCallback(async (): Promise<boolean> => {
    log.info("mhc", "🔄 Intentando reconexión MHC");

    setState(prev => ({ ...prev, connectionStatus: "connecting" }));

    try {
      await initializeMHC();
      return state.connectionStatus === "connected";
    } catch (error) {
      log.error("mhc", "❌ Error en reconexión", error);
      return false;
    }
  }, [initializeMHC, state.connectionStatus]);

  const getDeviceInfo = useCallback((): object => {
    return {
      isAvailable: state.isAvailable,
      deviceId: state.deviceId,
      connectionStatus: state.connectionStatus,
      capabilities: state.capabilities,
      mhcVersion: "unknown", // Could be enhanced to get actual version
      timestamp: new Date().toISOString(),
    };
  }, [state]);

  // ========== GAME-SPECIFIC MHC ==========
  const announceGameStart = useCallback((gameMode: string) => {
    if (!state.capabilities.canSpeak) return;

    const messages = {
      "yo_pienso": "🎮 Modo: Tú piensas, yo adivino",
      "aura_piensa": "🎮 Modo: Yo pienso, tú adivinas",
    };

    const message = messages[gameMode as keyof typeof messages] || `🎮 Juego iniciado: ${gameMode}`;
    sendSystemMessage(message, "info");
  }, [state.capabilities.canSpeak, sendSystemMessage]);

  const announceGameEnd = useCallback((result: "win" | "lose" | "draw") => {
    if (!state.capabilities.canSpeak) return;

    const messages = {
      win: "🏆 ¡Victoria! ¡Excelente juego!",
      lose: "😔 Derrota, pero buen intento",
      draw: "🤝 Empate - ¡Buen juego!",
    };

    sendSystemMessage(messages[result], "info");
  }, [state.capabilities.canSpeak, sendSystemMessage]);

  const showGameProgress = useCallback((current: number, total: number) => {
    if (!state.capabilities.canSendMessages) return;

    const percentage = Math.round((current / total) * 100);
    const progressBar = "█".repeat(Math.floor(percentage / 10)) + "░".repeat(10 - Math.floor(percentage / 10));

    sendAura(`📊 Progreso: ${progressBar} ${current}/${total} (${percentage}%)`);
  }, [state.capabilities.canSendMessages, sendAura]);

  // ========== UTILITIES ==========
  const isFeatureAvailable = useCallback((feature: keyof MHCCapabilities): boolean => {
    return state.capabilities[feature];
  }, [state.capabilities]);

  const testAllFeatures = useCallback(async (): Promise<MHCCapabilities> => {
    if (!mhcInstance) {
      return {
        canSpeak: false,
        canCloseWebView: false,
        canSetTimeout: false,
        canHideAura: false,
        canSendMessages: false,
      };
    }

    const capabilities: MHCCapabilities = {
      canSpeak: false,
      canCloseWebView: false,
      canSetTimeout: false,
      canHideAura: false,
      canSendMessages: false,
    };

    // Test each capability
    try {
      mhcInstance.getId(); // Si esto funciona, MHC está disponible
      capabilities.canSpeak = true;
      capabilities.canCloseWebView = true;
      capabilities.canSetTimeout = true;
      capabilities.canHideAura = true;
      capabilities.canSendMessages = true;
    } catch (error) {
      log.warn("mhc", "⚠️ Algunas funcionalidades MHC no están disponibles", error);
    }

    return capabilities;
  }, [mhcInstance]);

  const reset = useCallback(() => {
    log.info("mhc", "🔄 Reseteando MHC");

    setState({
      isAvailable: false,
      deviceId: "",
      connectionStatus: "disconnected",
      capabilities: {
        canSpeak: false,
        canCloseWebView: false,
        canSetTimeout: false,
        canHideAura: false,
        canSendMessages: false,
      },
      errorMessage: null,
    });

    setMhcInstance(null);
  }, []);

  // ========== CONTEXT VALUE ==========
  const contextValue: MHCContextProps = {
    // Current state
    state,

    // Core MHC functionality
    setSucwTimeout,
    closeWebView,
    hideAura,
    speakAura,
    sendAura,
    getId,

    // Enhanced MHC operations
    speakAndHide,
    sendSystemMessage,
    setAppTimeout,

    // Connection management
    checkConnection,
    reconnect,
    getDeviceInfo,

    // Game-specific MHC
    announceGameStart,
    announceGameEnd,
    showGameProgress,

    // Utilities
    isFeatureAvailable,
    testAllFeatures,
    reset,
  };

  return (
    <MHCContext.Provider value={contextValue}>
      {children}
    </MHCContext.Provider>
  );
};
