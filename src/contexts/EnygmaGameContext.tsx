import {
  create<PERSON>ontext,
  useContext,
  useState,
  use<PERSON><PERSON>back,
  type ReactNode,
} from "react";
import { aiService, type GameMode } from "../services/AIService";
import { log } from "../services/LogService";

// Enhanced types for the game
export type GamePhase = "setup" | "questioning" | "guessing" | "finished";
export type PlayerRole = "guesser" | "answerer";

export interface GameSession {
  id: string;
  mode: GameMode;
  phase: GamePhase;
  startTime: Date;
  endTime?: Date;

  // Game state
  questionCount: number;
  maxQuestions: number;
  currentCharacter?: string;
  aiConfidence: number;

  // AÑADIR: playerRole debe estar aquí
  playerRole: PlayerRole; // 🔧 FIX: Agregar playerRole a GameSession

  // Messages with enhanced metadata
  messages: GameMessage[];

  // Game result
  winner?: "ai" | "user" | "draw";
  finalGuess?: string;
  wasCorrect?: boolean;
}

export interface GameMessage {
  id: string;
  text: string;
  sender: "user" | "ai";
  timestamp: Date;
  type: "question" | "answer" | "guess" | "hint" | "system";
  confidence?: number; // For AI messages
  validatedResponse?: "yes" | "no" | "maybe" | "unknown"; // For user answers
}

export interface GameInsight {
  suggestedQuestions: string[];
  categoryProgress: {
    person: boolean;
    profession: boolean;
    appearance: boolean;
    era: boolean;
    nationality: boolean;
  };
  likelyCharacters: string[];
  confidenceLevel: number;
}

interface EnygmaGameContextProps {
  // Current session
  session: GameSession | null;
  currentPhase: GamePhase;
  playerRole: PlayerRole;

  // Game actions
  startNewGame: (mode: GameMode, character?: string) => Promise<void>;
  askQuestion: (question: string) => Promise<void>;
  respondToQuestion: (
    response: "yes" | "no" | "maybe" | "unknown"
  ) => Promise<void>;
  makeGuess: (character: string) => Promise<boolean>;
  endGame: (reason: "victory" | "defeat" | "timeout" | "quit") => void;

  // Game intelligence
  getGameInsights: () => GameInsight;
  getSuggestedResponses: () => string[];
  getProgressSummary: () => string;

  // Computed properties
  canAskQuestion: boolean;
  canMakeGuess: boolean;
  questionsRemaining: number;
  gameProgress: number; // 0-100%

  // Game helpers
  validateUserInput: (input: string) => {
    isValid: boolean;
    suggestion?: string;
  };
  getHint: () => string | null;
}

const EnygmaGameContext = createContext<EnygmaGameContextProps | undefined>(
  undefined
);

export const useEnygmaGame = () => {
  const context = useContext(EnygmaGameContext);
  if (!context) {
    throw new Error("useEnygmaGame must be used within EnygmaGameProvider");
  }
  return context;
};

export const EnygmaGameProvider = ({ children }: { children: ReactNode }) => {
  const [session, setSession] = useState<GameSession | null>(null);

  // Computed values
  const currentPhase: GamePhase = session?.phase || "setup";
  const playerRole: PlayerRole = session?.mode === "yo_pienso" ? "answerer" : "guesser"; // 🔧 REMOVER: Ya no es computed
  const canAskQuestion =
    session?.phase === "questioning" &&
    session.questionCount < session.maxQuestions;
  const canMakeGuess =
    session?.phase === "questioning" || session?.phase === "guessing";
  const questionsRemaining = session
    ? session.maxQuestions - session.questionCount
    : 20;
  const gameProgress = session
    ? (session.questionCount / session.maxQuestions) * 100
    : 0;

  // Start new game with enhanced setup
  const startNewGame = useCallback(
    async (mode: GameMode, character?: string): Promise<void> => {
      log.info("game", `Starting new game: ${mode}`, { character });

      const newSession: GameSession = {
        id: `game-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        mode,
        phase: "questioning",
        startTime: new Date(),
        questionCount: 0,
        maxQuestions: 20,
        currentCharacter: character,
        aiConfidence: 0,
        messages: [],
        playerRole: mode === "yo_pienso" ? "answerer" : "guesser", // 🔧 FIX: Calcular playerRole
      };

      // 🔧 FIX: No generar mensaje inicial aquí - el GameOrchestrator se encarga del speech
      // Solo crear la sesión sin llamadas a la IA
      setSession(newSession);

      log.success("game", "Game started successfully", {
        sessionId: newSession.id,
      });
    },
    []
  );

  // Ask question (when user is guesser)
  const askQuestion = useCallback(
    async (question: string): Promise<void> => {
      if (!session || !canAskQuestion) {
        log.warn("game", "Cannot ask question in current state");
        return;
      }

      log.info("game", `User asking question: ${question}`);

      // Add user message
      const userMessage: GameMessage = {
        id: `msg-${Date.now()}`,
        text: question,
        sender: "user",
        timestamp: new Date(),
        type: "question",
      };

      try {
        // Get AI response
        const response = await aiService.generateResponse(
          question,
          session.mode
        );

        if (response.ok) {
          const aiMessage: GameMessage = {
            id: `msg-${Date.now() + 1}`,
            text: response.output,
            sender: "ai",
            timestamp: new Date(),
            type: "answer",
          };

          // Update session
          setSession((prev) =>
            prev
              ? {
                  ...prev,
                  questionCount: prev.questionCount + 1,
                  messages: [...prev.messages, userMessage, aiMessage],
                  phase:
                    prev.questionCount + 1 >= prev.maxQuestions
                      ? "guessing"
                      : "questioning",
                }
              : null
          );

          log.success("game", "Question processed successfully");
        } else {
          throw new Error("Failed to get AI response");
        }
      } catch (error) {
        log.error("game", "Failed to process question", error);
        throw error;
      }
    },
    [session, canAskQuestion]
  );

  // Respond to question (when user is answerer)
  const respondToQuestion = useCallback(
    async (response: "yes" | "no" | "maybe" | "unknown"): Promise<void> => {
      if (!session || session.mode !== "yo_pienso") {
        log.warn("game", "Cannot respond in current mode");
        return;
      }

      log.info("game", `User responding: ${response}`);

      const responseText = {
        yes: "Sí",
        no: "No",
        maybe: "Tal vez",
        unknown: "No lo sé",
      }[response];

      // Add user response
      const userMessage: GameMessage = {
        id: `msg-${Date.now()}`,
        text: responseText,
        sender: "user",
        timestamp: new Date(),
        type: "answer",
        validatedResponse: response,
      };

      try {
        // Get next AI question or guess
        const aiResponse = await aiService.generateResponse(
          responseText,
          session.mode
        );

        if (aiResponse.ok) {
          const isGuess =
            aiResponse.output.toLowerCase().includes("creo que") ||
            aiResponse.output.toLowerCase().includes("es ") ||
            aiResponse.output.toLowerCase().includes("¿es ");

          const aiMessage: GameMessage = {
            id: `msg-${Date.now() + 1}`,
            text: aiResponse.output,
            sender: "ai",
            timestamp: new Date(),
            type: isGuess ? "guess" : "question",
            confidence:
              session.aiConfidence +
              (response === "yes" ? 10 : response === "no" ? -5 : 0),
          };

          // Update session
          setSession((prev) =>
            prev
              ? {
                  ...prev,
                  questionCount: prev.questionCount + 1,
                  messages: [...prev.messages, userMessage, aiMessage],
                  phase: isGuess
                    ? "guessing"
                    : prev.questionCount + 1 >= prev.maxQuestions
                      ? "guessing"
                      : "questioning",
                  aiConfidence: Math.max(
                    0,
                    Math.min(100, aiMessage.confidence || 0)
                  ),
                }
              : null
          );

          log.success("game", "Response processed successfully");
        }
      } catch (error) {
        log.error("game", "Failed to process response", error);
        throw error;
      }
    },
    [session]
  );

  // Make a guess
  const makeGuess = useCallback(
    async (character: string): Promise<boolean> => {
      if (!session) return false;

      log.info("game", `Making guess: ${character}`);

      const guessMessage: GameMessage = {
        id: `msg-${Date.now()}`,
        text: `¿Es ${character}?`,
        sender: session.mode === "aura_piensa" ? "user" : "ai",
        timestamp: new Date(),
        type: "guess",
      };

      // In a real implementation, this would validate against the actual character
      // For now, we'll simulate
      const isCorrect = Math.random() > 0.7; // 30% chance of correct guess

      setSession((prev) =>
        prev
          ? {
              ...prev,
              phase: "finished",
              endTime: new Date(),
              messages: [...prev.messages, guessMessage],
              winner: isCorrect
                ? session.mode === "aura_piensa"
                  ? "user"
                  : "ai"
                : undefined,
              finalGuess: character,
              wasCorrect: isCorrect,
            }
          : null
      );

      return isCorrect;
    },
    [session]
  );

  // Get game insights for AI assistance
  const getGameInsights = useCallback((): GameInsight => {
    if (!session) {
      return {
        suggestedQuestions: [],
        categoryProgress: {
          person: false,
          profession: false,
          appearance: false,
          era: false,
          nationality: false,
        },
        likelyCharacters: [],
        confidenceLevel: 0,
      };
    }

    // Analyze messages to determine what's been asked
    const askedAbout = {
      person: session.messages.some(
        (m) =>
          m.text.toLowerCase().includes("persona") ||
          m.text.toLowerCase().includes("real")
      ),
      profession: session.messages.some(
        (m) =>
          m.text.toLowerCase().includes("actor") ||
          m.text.toLowerCase().includes("trabajo")
      ),
      appearance: session.messages.some(
        (m) =>
          m.text.toLowerCase().includes("pelo") ||
          m.text.toLowerCase().includes("alto")
      ),
      era: session.messages.some(
        (m) =>
          m.text.toLowerCase().includes("vivo") ||
          m.text.toLowerCase().includes("siglo")
      ),
      nationality: session.messages.some(
        (m) =>
          m.text.toLowerCase().includes("americano") ||
          m.text.toLowerCase().includes("país")
      ),
    };

    const suggestions = [];
    if (!askedAbout.person) suggestions.push("¿Es una persona real?");
    if (!askedAbout.profession) suggestions.push("¿Es actor o actriz?");
    if (!askedAbout.era) suggestions.push("¿Está vivo actualmente?");
    if (!askedAbout.nationality) suggestions.push("¿Es de Estados Unidos?");

    return {
      suggestedQuestions: suggestions,
      categoryProgress: askedAbout,
      likelyCharacters: [], // Would be populated by AI analysis
      confidenceLevel: session.aiConfidence,
    };
  }, [session]);

  // Get suggested responses for current context
  const getSuggestedResponses = useCallback((): string[] => {
    if (!session) return [];

    if (session.mode === "yo_pienso") {
      return ["Sí", "No", "Tal vez", "No lo sé"];
    } else {
      const insights = getGameInsights();
      return insights.suggestedQuestions.slice(0, 3);
    }
  }, [session, getGameInsights]);

  // Validate user input
  const validateUserInput = useCallback(
    (input: string): { isValid: boolean; suggestion?: string } => {
      if (!input.trim()) {
        return { isValid: false, suggestion: "Por favor, escribe algo" };
      }

      if (session?.mode === "yo_pienso") {
        const validResponses = [
          "sí",
          "si",
          "no",
          "tal vez",
          "quizás",
          "no lo sé",
          "no sé",
        ];
        const isValid = validResponses.some((response) =>
          input.toLowerCase().includes(response)
        );

        if (!isValid) {
          return {
            isValid: false,
            suggestion: "Responde solo con: Sí, No, Tal vez, o No lo sé",
          };
        }
      }

      if (input.length > 200) {
        return {
          isValid: false,
          suggestion: "La pregunta es muy larga. Sé más conciso.",
        };
      }

      return { isValid: true };
    },
    [session]
  );

  // Get contextual hint
  const getHint = useCallback((): string | null => {
    if (!session) return null;

    const insights = getGameInsights();

    if (session.questionCount < 5) {
      return "Empieza con preguntas amplias para descartar categorías grandes";
    }

    if (session.questionCount > 15) {
      return "¡Te quedan pocas preguntas! Es hora de hacer suposiciones específicas";
    }

    if (insights.suggestedQuestions.length > 0) {
      return `Considera preguntar: ${insights.suggestedQuestions[0]}`;
    }

    return null;
  }, [session, getGameInsights]);

  const getProgressSummary = useCallback((): string => {
    if (!session) return "";

    return `Pregunta ${session.questionCount}/${session.maxQuestions} - ${Math.round(gameProgress)}% completado`;
  }, [session, gameProgress]);

  const endGame = useCallback(
    (reason: "victory" | "defeat" | "timeout" | "quit") => {
      if (!session) return;

      log.info("game", `Ending game: ${reason}`, { sessionId: session.id });

      setSession((prev) =>
        prev
          ? {
              ...prev,
              phase: "finished",
              endTime: new Date(),
              winner:
                reason === "victory"
                  ? "user"
                  : reason === "defeat"
                    ? "ai"
                    : undefined,
            }
          : null
      );
    },
    [session]
  );

  return (
    <EnygmaGameContext.Provider
      value={{
        session,
        currentPhase,
        playerRole,
        startNewGame,
        askQuestion,
        respondToQuestion,
        makeGuess,
        endGame,
        getGameInsights,
        getSuggestedResponses,
        getProgressSummary,
        canAskQuestion,
        canMakeGuess,
        questionsRemaining,
        gameProgress,
        validateUserInput,
        getHint,
      }}
    >
      {children}
    </EnygmaGameContext.Provider>
  );
};
