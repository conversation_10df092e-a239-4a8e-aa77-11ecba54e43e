import {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  type ReactNode,
} from "react";
import { log } from "../services/LogService";

// ========== TYPES ==========
export interface AppConfig {
  speechApiUrl: string | null;
  speechApiKey: string | null;
  iaApiUrl: string | null;
  movistarApiUrl: string | null;
  mode: string;
}

export interface DebugFlags {
  movistarPlus: boolean;
  speech: boolean;
  transcription: boolean;
  ai: boolean;
}

export interface AppError {
  id: string;
  message: string;
  timestamp: Date;
  context?: string;
  error?: Error;
}

export type AppState = "initializing" | "ready" | "error";

// ========== INTERFACES ==========
interface AppContextProps {
  // Estado de la aplicación
  appState: AppState;
  isInitialized: boolean;

  // Configuración
  config: AppConfig;
  debugFlags: DebugFlags;

  // Manejo de errores
  errors: AppError[];
  addError: (message: string, context?: string, error?: Error) => void;
  clearErrors: () => void;
  clearError: (id: string) => void;

  // Utilidades
  isConfigValid: () => boolean;
  getConfigStatus: () => Record<string, boolean>;

  // Métodos de inicialización
  initialize: () => Promise<void>;
  reset: () => void;
}

// ========== CONTEXT ==========
const AppContext = createContext<AppContextProps | undefined>(undefined);

export const useAppContext = () => {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error("useAppContext must be used within an AppProvider");
  }
  return context;
};

export const AppProvider = ({ children }: { children: ReactNode }) => {
  // ========== ESTADOS ==========
  const [appState, setAppState] = useState<AppState>("initializing");
  const [isInitialized, setIsInitialized] = useState<boolean>(false);
  const [errors, setErrors] = useState<AppError[]>([]);

  // ========== CONFIGURACIÓN ==========
  const config: AppConfig = {
    speechApiUrl: import.meta.env.VITE_SPEECH_API_URL || null,
    speechApiKey: import.meta.env.VITE_SPEECH_API_KEY || null,
    iaApiUrl: import.meta.env.VITE_IA_API_URL || null,
    movistarApiUrl: import.meta.env.VITE_MOVISTAR_API_URL || null,
    mode: import.meta.env.MODE || "development",
  };

  const debugFlags: DebugFlags = {
    movistarPlus: import.meta.env.VITE_DEBUG_MOVISTARPLUS === "true",
    speech: import.meta.env.VITE_DEBUG_SPEECH === "true",
    transcription: import.meta.env.VITE_DEBUG_TRANSCRIPTION === "true",
    ai: import.meta.env.VITE_DEBUG_AI === "true",
  };

  // ========== EFECTOS ==========
  useEffect(() => {
    initialize();
  }, []);

  // Manejo de errores globales
  useEffect(() => {
    const handleGlobalError = (event: ErrorEvent) => {
      addError(
        `Error global: ${event.message}`,
        `${event.filename}:${event.lineno}:${event.colno}`,
        event.error
      );
    };

    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      addError(
        `Promise rechazada: ${event.reason}`,
        "Unhandled Promise Rejection",
        event.reason instanceof Error ? event.reason : undefined
      );
    };

    window.addEventListener("error", handleGlobalError);
    window.addEventListener("unhandledrejection", handleUnhandledRejection);

    return () => {
      window.removeEventListener("error", handleGlobalError);
      window.removeEventListener("unhandledrejection", handleUnhandledRejection);
    };
  }, []);

  // ========== FUNCIONES ==========
  const addError = useCallback((message: string, context?: string, error?: Error) => {
    const appError: AppError = {
      id: `error-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      message,
      timestamp: new Date(),
      context,
      error,
    };

    setErrors(prev => [...prev, appError]);

    // Log del error
    log.error("app", message, {
      context,
      error: error?.message,
      stack: error?.stack,
    });
  }, []);

  const clearErrors = useCallback(() => {
    setErrors([]);
    log.info("app", "Errores limpiados");
  }, []);

  const clearError = useCallback((id: string) => {
    setErrors(prev => prev.filter(error => error.id !== id));
    log.debug("app", `Error ${id} eliminado`);
  }, []);

  const isConfigValid = useCallback((): boolean => {
    return !!(config.speechApiUrl && config.speechApiKey && config.iaApiUrl);
  }, [config]);

  const getConfigStatus = useCallback((): Record<string, boolean> => {
    return {
      speechApiUrl: !!config.speechApiUrl,
      speechApiKey: !!config.speechApiKey,
      iaApiUrl: !!config.iaApiUrl,
      movistarApiUrl: !!config.movistarApiUrl,
    };
  }, [config]);

  const initialize = useCallback(async () => {
    try {
      setAppState("initializing");

      log.info("app", "🚀 Inicializando aplicación Enygma");

      // Log de configuración
      log.debug("app", "Variables de entorno cargadas", getConfigStatus());

      // Log de debug flags
      if (Object.values(debugFlags).some(flag => flag)) {
        log.warn("app", "Modo debug activado", debugFlags);
      }

      // Validar configuración crítica
      if (!isConfigValid()) {
        throw new Error("Configuración incompleta: faltan variables de entorno críticas");
      }

      setAppState("ready");
      setIsInitialized(true);

      log.success("app", "✅ Aplicación inicializada correctamente");

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Error desconocido";
      setAppState("error");
      addError(`Error en inicialización: ${errorMessage}`, "initialize", error instanceof Error ? error : undefined);
    }
  }, [config, debugFlags, isConfigValid, getConfigStatus, addError]);

  const reset = useCallback(() => {
    log.info("app", "🔄 Reseteando aplicación");
    setAppState("initializing");
    setIsInitialized(false);
    clearErrors();
    initialize();
  }, [initialize, clearErrors]);

  return (
    <AppContext.Provider
      value={{
        // Estado de la aplicación
        appState,
        isInitialized,

        // Configuración
        config,
        debugFlags,

        // Manejo de errores
        errors,
        addError,
        clearErrors,
        clearError,

        // Utilidades
        isConfigValid,
        getConfigStatus,

        // Métodos de inicialización
        initialize,
        reset,
      }}
    >
      {children}
    </AppContext.Provider>
  );
};

// ========== EXPORT TYPES ==========
// Los tipos ya están exportados arriba con export interface/type
