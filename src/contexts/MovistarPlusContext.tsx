import {
  createContext,
  useContext,
  useState,
  useCallback,
  type ReactNode,
} from "react";
import {
  movistarPlusService,
  type FilmData,
  type MovistarPlusResponse,
} from "../services/MovistarPlusService";

// ========== INTERFACES ==========
interface MovistarPlusContextProps {
  // Estados
  isLoading: boolean;
  error: string | null;

  // Métodos principales
  getMovistarPlusFilms: (film: FilmData) => Promise<MovistarPlusResponse>;
  searchByTitle: (title: string) => Promise<MovistarPlusResponse>;
  setFilmToLaunch: (film: string | null) => void;
  reset: () => void;

  // Utilidades
  testConnection: () => Promise<boolean>;
  isConfigured: () => boolean;

  // Getters
  filmToLaunch: string | null;
}

// ========== CONTEXT ==========
const MovistarPlusContext = createContext<MovistarPlusContextProps | undefined>(
  undefined,
);

export const useMovistarPlusContext = () => {
  const context = useContext(MovistarPlusContext);
  if (!context) {
    throw new Error(
      "useMovistarPlusContext must be used within a MovistarPlusProvider",
    );
  }
  return context;
};

export const MovistarPlusProvider = ({ children }: { children: ReactNode }) => {
  // ========== ESTADOS ==========
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // ========== COMPUTED VALUES ==========
  const filmToLaunch = movistarPlusService.getFilmToLaunch();

  // ========== FUNCIONES WRAPPER ==========
  const getMovistarPlusFilms = useCallback(
    async (film: FilmData): Promise<MovistarPlusResponse> => {
      setIsLoading(true);
      setError(null);

      try {
        console.log("🎬 MovistarPlusContext: Buscando contenido...", film);

        const response = await movistarPlusService.getMovistarPlusFilms(film);

        if (!response.success) {
          setError(response.error || "Error desconocido");
        } else {
          console.log("✅ MovistarPlusContext: Contenido encontrado:", {
            titlesCount: response.titles?.length || 0,
            hasData: Boolean(response.data),
          });
        }

        return response;
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Error desconocido";
        console.error(
          "❌ MovistarPlusContext: Error en getMovistarPlusFilms:",
          error,
        );

        setError(`Error al buscar contenido: ${errorMessage}`);

        return {
          success: false,
          error: errorMessage,
          data: null,
          titles: [],
        };
      } finally {
        setIsLoading(false);
      }
    },
    [],
  );

  const searchByTitle = useCallback(
    async (title: string): Promise<MovistarPlusResponse> => {
      console.log("🔍 MovistarPlusContext: Búsqueda por título:", title);
      return getMovistarPlusFilms({
        title,
        recommendation: `Búsqueda de ${title}`,
      });
    },
    [getMovistarPlusFilms],
  );

  const setFilmToLaunch = useCallback((film: string | null) => {
    console.log(
      "🎬 MovistarPlusContext: Configurando película para lanzar:",
      film,
    );
    movistarPlusService.setFilmToLaunch(film);
  }, []);

  const reset = useCallback(() => {
    console.log("🧹 MovistarPlusContext: Reseteando contexto");
    setError(null);
    setIsLoading(false);
    movistarPlusService.reset();
  }, []);

  // ========== UTILIDADES ==========
  const testConnection = useCallback(async (): Promise<boolean> => {
    setIsLoading(true);
    setError(null);

    try {
      console.log("🔗 MovistarPlusContext: Probando conexión...");
      const isConnected = await movistarPlusService.testConnection();

      if (!isConnected) {
        setError("No se pudo conectar con la API de Movistar+");
      } else {
        console.log("✅ MovistarPlusContext: Conexión exitosa");
      }

      return isConnected;
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Error de conexión";
      console.error(
        "❌ MovistarPlusContext: Error en test de conexión:",
        error,
      );
      setError(errorMessage);
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const isConfigured = useCallback((): boolean => {
    const configured = movistarPlusService.isConfigured();
    console.log("⚙️ MovistarPlusContext: API configurada:", configured);
    return configured;
  }, []);

  return (
    <MovistarPlusContext.Provider
      value={{
        // Estados
        isLoading,
        error,

        // Métodos principales
        getMovistarPlusFilms,
        searchByTitle,
        setFilmToLaunch,
        reset,

        // Utilidades
        testConnection,
        isConfigured,

        // Getters
        filmToLaunch,
      }}
    >
      {children}
    </MovistarPlusContext.Provider>
  );
};

// ========== EXPORT TYPES ==========
export type { FilmData, MovistarPlusResponse };
