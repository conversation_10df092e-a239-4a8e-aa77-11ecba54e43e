import {
  createContext,
  useContext,
  useEffect,
  useState,
  useCallback,
  type ReactNode,
} from "react";
import {
  transcriptionService,
  type TranscriptionEvent,
  type IMHC,
  MHC,
  normalize,
} from "../services/TranscriptionService";

// ========== INTERFACES ==========
interface TranscriptionContextProps {
  // Estados principales
  transcription: string | null;
  setTranscription: React.Dispatch<React.SetStateAction<string | null>>;
  isListening: boolean;

  // Métodos de control
  startListening: () => void;
  stopListening: () => void;
  simulateTranscription: (text: string) => void;
  clearTranscription: () => void;
  reset: () => void;

  // MHC methods (proxy del servicio)
  mhc: IMHC;
  setSucwTimeout: (timeMS: number) => void;
  closeWebView: () => void;
  hideAura: () => void;
  speakAura: (text: string) => void;
  sendAura: (text: string) => void;
  getId: () => string;

  // Utilidades
  isMHCAvailable: () => boolean;
  getStatus: () => object;
  normalize: (text: string) => Promise<string>;
}

// ========== CONTEXT ==========
const TranscriptionContext = createContext<
  TranscriptionContextProps | undefined
>(undefined);

export const useTranscriptionContext = () => {
  const context = useContext(TranscriptionContext);
  if (!context) {
    throw new Error(
      "useTranscriptionContext must be used within a TranscriptionProvider",
    );
  }
  return context;
};

export const TranscriptionProvider = ({
  children,
}: {
  children: ReactNode;
}) => {
  // ========== ESTADOS ==========
  const [transcription, setTranscription] = useState<string | null>(null);

  // ========== COMPUTED VALUES ==========
  const isListening = transcriptionService.isCurrentlyListening();
  const mhc = transcriptionService.getMHC();

  // ========== EFECTOS ==========
  useEffect(() => {
    console.log("🎤 TranscriptionContext: Inicializando contexto");

    // Añadir listener para eventos de transcripción
    const removeListener = transcriptionService.addEventListener(
      (event: TranscriptionEvent) => {
        console.log("📡 TranscriptionContext: Evento recibido:", event);

        switch (event.type) {
          case "transcription":
            if (event.normalized) {
              setTranscription(event.normalized);
              console.log(
                "✅ TranscriptionContext: Transcripción actualizada:",
                event.normalized,
              );
            }
            break;

          case "command":
            console.log(
              "🎯 TranscriptionContext: Comando ejecutado:",
              event.data,
            );
            // Los comandos de cierre se manejan automáticamente en el servicio
            break;

          case "error":
            console.error(
              "❌ TranscriptionContext: Error de transcripción:",
              event.data,
            );
            break;

          default:
            console.warn(
              "⚠️ TranscriptionContext: Tipo de evento desconocido:",
              event.type,
            );
        }
      },
    );

    // Iniciar escucha automáticamente
    transcriptionService.startListening();

    // Cleanup al desmontar
    return () => {
      console.log("🧹 TranscriptionContext: Limpiando contexto");
      removeListener();
      transcriptionService.stopListening();
    };
  }, []);

  // Sincronizar transcripción actual del servicio
  useEffect(() => {
    const currentTranscription = transcriptionService.getCurrentTranscription();
    if (currentTranscription !== transcription) {
      setTranscription(currentTranscription);
    }
  }, [transcription]);

  // ========== FUNCIONES WRAPPER ==========
  const startListening = useCallback(() => {
    console.log("🎤 TranscriptionContext: Iniciando escucha");
    transcriptionService.startListening();
  }, []);

  const stopListening = useCallback(() => {
    console.log("🔇 TranscriptionContext: Deteniendo escucha");
    transcriptionService.stopListening();
  }, []);

  const simulateTranscription = useCallback((text: string) => {
    console.log("🧪 TranscriptionContext: Simulando transcripción:", text);
    transcriptionService.simulateTranscription(text);
  }, []);

  const clearTranscription = useCallback(() => {
    console.log("🧹 TranscriptionContext: Limpiando transcripción");
    setTranscription(null);
    transcriptionService.clearTranscription();
  }, []);

  const reset = useCallback(() => {
    console.log("🔄 TranscriptionContext: Reseteando contexto");
    setTranscription(null);
    transcriptionService.reset();
  }, []);

  // ========== MHC PROXY METHODS ==========
  const setSucwTimeout = useCallback((timeMS: number) => {
    transcriptionService.setSucwTimeout(timeMS);
  }, []);

  const closeWebView = useCallback(() => {
    transcriptionService.closeWebView();
  }, []);

  const hideAura = useCallback(() => {
    transcriptionService.hideAura();
  }, []);

  const speakAura = useCallback((text: string) => {
    transcriptionService.speakAura(text);
  }, []);

  const sendAura = useCallback((text: string) => {
    transcriptionService.sendAura(text);
  }, []);

  const getId = useCallback((): string => {
    return transcriptionService.getId();
  }, []);

  // ========== UTILITY METHODS ==========
  const isMHCAvailable = useCallback((): boolean => {
    return transcriptionService.isMHCAvailable();
  }, []);

  const getStatus = useCallback((): object => {
    return transcriptionService.getStatus();
  }, []);

  return (
    <TranscriptionContext.Provider
      value={{
        // Estados principales
        transcription,
        setTranscription,
        isListening,

        // Métodos de control
        startListening,
        stopListening,
        simulateTranscription,
        clearTranscription,
        reset,

        // MHC methods (proxy del servicio)
        mhc,
        setSucwTimeout,
        closeWebView,
        hideAura,
        speakAura,
        sendAura,
        getId,

        // Utilidades
        isMHCAvailable,
        getStatus,
        normalize,
      }}
    >
      {children}
    </TranscriptionContext.Provider>
  );
};

// ========== EXPORT UTILITIES ==========
export { normalize, MHC };
export type { IMHC, TranscriptionEvent };
