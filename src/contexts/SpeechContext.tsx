import {
  createContext,
  useContext,
  useState,
  type ReactNode,
  useEffect,
} from "react";
import { speechService } from "../services/SpeechService";

// ========== INTERFACES ==========
interface SpeechContextProps {
  isLocutionActivated: boolean;
  setIsLocutionActivated: React.Dispatch<React.SetStateAction<boolean>>;
  getSpeech: (message: string) => Promise<string>;
  isAudioOnPlay: boolean | null;
  setIsAudioOnPlay: React.Dispatch<React.SetStateAction<boolean | null>>;
  onClickSoundBtn: () => void;
  configVoice: (genre: string) => Promise<boolean>;
  toSpeech: (url: string) => void;
  setSpeech: (url: string) => void;
  isConfiguring: boolean;
  availableVoices: string[];
  currentVoiceId: string;
  speak: (text: string) => Promise<void>;
  speakWithAutoConfig: (text: string, genre?: string) => Promise<void>;
}

// ========== CONTEXT ==========
const SpeechContext = createContext<SpeechContextProps | undefined>(undefined);

export const useSpeechContext = () => {
  const context = useContext(SpeechContext);
  if (!context) {
    throw new Error("useSpeechContext must be used within a SpeechProvider");
  }
  return context;
};

export const SpeechProvider = ({ children }: { children: ReactNode }) => {
  // ========== ESTADOS ==========
  const [isLocutionActivated, setIsLocutionActivated] =
    useState<boolean>(false);
  const [isAudioOnPlay, setIsAudioOnPlay] = useState<boolean | null>(false);
  const [isConfiguring, setIsConfiguring] = useState<boolean>(false);
  const [availableVoices, setAvailableVoices] = useState<string[]>([]);
  const [currentVoiceId, setCurrentVoiceId] = useState<string>("");

  // ========== EFECTOS ==========
  useEffect(() => {
    const handleAudioPlayback = async () => {
      if (isAudioOnPlay !== null) {
        if (isAudioOnPlay) {
          try {
            await speechService.playSpeech();
          } catch (error) {
            console.error("❌ SpeechContext: Error reproduciendo audio:", error);
          }
        } else {
          speechService.stopSpeech();
        }
      } else {
        speechService.noSpeech();
      }
    };

    handleAudioPlayback();
  }, [isAudioOnPlay]);

  // ========== FUNCIONES WRAPPER ==========
  const configVoice = async (genre: string): Promise<boolean> => {
    setIsConfiguring(true);
    console.log("🔧 SpeechContext: Iniciando configuración de voz...");

    try {
      const success = await speechService.configVoice(genre);
      console.log("🔧 SpeechContext: Resultado del servicio:", success);

      if (success) {
        // Actualizar estados del contexto
        setIsLocutionActivated(true);
        setAvailableVoices(speechService.getAvailableVoicesList());
        setCurrentVoiceId(speechService.getCurrentVoiceId());
        console.log(
          "✅ SpeechContext: Estados actualizados - isLocutionActivated -> true",
        );
        return true;
      } else {
        setIsAudioOnPlay(null);
        setIsLocutionActivated(false);
        console.warn("⚠️ SpeechContext: Configuración falló");
        return false;
      }
    } catch (error) {
      console.error("❌ SpeechContext: Error configurando voz:", error);
      setIsAudioOnPlay(null);
      setIsLocutionActivated(false);
      return false;
    } finally {
      setIsConfiguring(false);
      console.log("🏁 SpeechContext: Configuración terminada");
    }
  };

  const getSpeech = async (message: string): Promise<string> => {
    try {
      if (!isLocutionActivated) {
        throw new Error("Voz no configurada. Ejecuta configVoice() primero.");
      }
      return await speechService.getSpeech(message);
    } catch (error) {
      console.error("Error obteniendo audio:", error);
      setIsAudioOnPlay(null);
      throw error;
    }
  };

  const setSpeech = (url: string) => {
    speechService.setSpeech(url);
    setIsAudioOnPlay(false);
  };

  const toSpeech = async (url: string) => {
    try {
      await speechService.toSpeech(url);
      setIsAudioOnPlay(true);
    } catch (error) {
      console.error("❌ SpeechContext: Error en toSpeech:", error);
      setIsAudioOnPlay(null);
    }
  };

  const speak = async (text: string): Promise<void> => {
    console.log(
      "🎤 SpeechContext: speak() llamado con:",
      text.substring(0, 50),
    );
    console.log("🎤 SpeechContext: isLocutionActivated:", isLocutionActivated);

    try {
      if (!isLocutionActivated) {
        console.warn(
          "⚠️ SpeechContext: Voz no configurada, configurando automáticamente...",
        );
        const configured = await configVoice("female");
        if (!configured) {
          throw new Error("No se pudo configurar la voz automáticamente");
        }
      }

      const audioUrl = await getSpeech(text);
      console.log("✅ SpeechContext: Audio obtenido, reproduciendo...");
      await toSpeech(audioUrl);
    } catch (error) {
      console.error("❌ SpeechContext: Error en speak():", error);
      throw error;
    }
  };

  const speakWithAutoConfig = async (
    text: string,
    genre: string = "female",
  ): Promise<void> => {
    setIsConfiguring(true);
    try {
      await speechService.speakWithAutoConfig(text, genre);

      // Actualizar estados si no estaban configurados
      if (!isLocutionActivated) {
        setIsLocutionActivated(true);
        setAvailableVoices(speechService.getAvailableVoicesList());
        setCurrentVoiceId(speechService.getCurrentVoiceId());
      }
      setIsAudioOnPlay(true);
    } catch (error) {
      console.error("❌ SpeechContext: Error en speakWithAutoConfig():", error);
      setIsAudioOnPlay(null);
      throw error;
    } finally {
      setIsConfiguring(false);
    }
  };

  const onClickSoundBtn = () => {
    if (isAudioOnPlay !== null) {
      setIsAudioOnPlay(!isAudioOnPlay);
    }
  };

  return (
    <SpeechContext.Provider
      value={{
        isLocutionActivated,
        setIsLocutionActivated,
        getSpeech,
        isAudioOnPlay,
        setIsAudioOnPlay,
        onClickSoundBtn,
        configVoice,
        toSpeech,
        setSpeech,
        isConfiguring,
        availableVoices,
        currentVoiceId,
        speak,
        speakWithAutoConfig,
      }}
    >
      {children}
    </SpeechContext.Provider>
  );
};
