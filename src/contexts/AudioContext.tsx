import {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  type ReactNode,
} from "react";
import { speechService } from "../services/SpeechService";
import {
  transcriptionService,
  type TranscriptionEvent,
  type IMHC,
  MHC,
  normalize,
} from "../services/TranscriptionService";
import { log } from "../services/LogService";

// ========== TYPES ==========
export interface AudioInputState {
  transcription: string | null;
  isListening: boolean;
  isProcessing: boolean;
}

export interface AudioOutputState {
  isLocutionActivated: boolean;
  isAudioOnPlay: boolean | null;
  isConfiguring: boolean;
  availableVoices: string[];
  currentVoiceId: string;
}

export interface MHCState {
  isAvailable: boolean;
  id: string;
  status: object;
}

// ========== INTERFACES ==========
interface AudioContextProps {
  // ===== INPUT (Transcription) =====
  input: AudioInputState;

  // Métodos de control de entrada
  startListening: () => void;
  stopListening: () => void;
  simulateTranscription: (text: string) => void;
  clearTranscription: () => void;

  // ===== OUTPUT (Speech) =====
  output: AudioOutputState;

  // Métodos de control de salida
  configVoice: (genre: string) => Promise<boolean>;
  speak: (text: string) => Promise<void>;
  speakWithAutoConfig: (text: string, genre?: string) => Promise<void>;
  getSpeech: (message: string) => Promise<string>;
  toSpeech: (url: string) => void;
  setSpeech: (url: string) => void;
  onClickSoundBtn: () => void;

  // ===== MHC Integration =====
  mhc: MHCState & IMHC;

  // Métodos MHC
  setSucwTimeout: (timeMS: number) => void;
  closeWebView: () => void;
  hideAura: () => void;
  speakAura: (text: string) => void;
  sendAura: (text: string) => void;
  getId: () => string;
  isMHCAvailable: () => boolean;
  getStatus: () => object;

  // ===== UTILIDADES =====
  normalize: (text: string) => Promise<string>;
  reset: () => void;

  // Eventos
  addEventListener: (callback: (event: TranscriptionEvent) => void) => () => void;
}

// ========== CONTEXT ==========
const AudioContext = createContext<AudioContextProps | undefined>(undefined);

export const useAudioContext = () => {
  const context = useContext(AudioContext);
  if (!context) {
    throw new Error("useAudioContext must be used within an AudioProvider");
  }
  return context;
};

export const AudioProvider = ({ children }: { children: ReactNode }) => {
  // ========== ESTADOS INPUT ==========
  const [transcription, setTranscription] = useState<string | null>(null);
  const [isProcessing] = useState<boolean>(false);

  // ========== ESTADOS OUTPUT ==========
  const [isLocutionActivated, setIsLocutionActivated] = useState<boolean>(false);
  const [isAudioOnPlay, setIsAudioOnPlay] = useState<boolean | null>(false);
  const [isConfiguring, setIsConfiguring] = useState<boolean>(false);
  const [availableVoices, setAvailableVoices] = useState<string[]>([]);
  const [currentVoiceId, setCurrentVoiceId] = useState<string>("");

  // ========== ESTADOS MHC ==========
  const [mhcState, setMhcState] = useState({
    isListening: false,
    mhcInstance: transcriptionService.getMHC(),
    mhcId: transcriptionService.getId(),
    mhcStatus: transcriptionService.getStatus(),
    isMHCAvailable: transcriptionService.isMHCAvailable(),
  });

  // ========== EFECTOS ==========
  // Efecto para actualizar estado MHC
  useEffect(() => {
    const updateMhcState = () => {
      setMhcState({
        isListening: transcriptionService.isCurrentlyListening(),
        mhcInstance: transcriptionService.getMHC(),
        mhcId: transcriptionService.getId(),
        mhcStatus: transcriptionService.getStatus(),
        isMHCAvailable: transcriptionService.isMHCAvailable(),
      });
    };

    // Actualizar inmediatamente
    updateMhcState();

    // Actualizar periódicamente para cambios en MHC
    const interval = setInterval(updateMhcState, 1000);

    return () => clearInterval(interval);
  }, []);

  // Efecto para manejo de audio output
  useEffect(() => {
    if (isAudioOnPlay !== null) {
      isAudioOnPlay ? speechService.playSpeech() : speechService.stopSpeech();
    } else {
      speechService.noSpeech();
    }
  }, [isAudioOnPlay]);

  // Efecto para inicializar transcripción
  useEffect(() => {
    log.info("audio", "🎵 Inicializando AudioContext");

    // Listener para eventos de transcripción
    const removeListener = transcriptionService.addEventListener((event: TranscriptionEvent) => {
      log.debug("audio", "📝 Evento de transcripción recibido", {
        type: event.type,
        data: event.data.substring(0, 50),
      });

      if (event.type === "transcription") {
        setTranscription(event.normalized || event.data);
      }
    });

    // Iniciar escucha automáticamente
    transcriptionService.startListening();

    // Cleanup al desmontar
    return () => {
      log.info("audio", "🧹 Limpiando AudioContext");
      removeListener();
      transcriptionService.stopListening();
    };
  }, []);

  // Sincronizar transcripción actual del servicio
  useEffect(() => {
    const currentTranscription = transcriptionService.getCurrentTranscription();
    if (currentTranscription !== transcription) {
      setTranscription(currentTranscription);
    }
  }, [transcription]);

  // ========== FUNCIONES INPUT ==========
  const startListening = useCallback(() => {
    log.info("audio", "🎤 Iniciando escucha");
    transcriptionService.startListening();
  }, []);

  const stopListening = useCallback(() => {
    log.info("audio", "🛑 Deteniendo escucha");
    transcriptionService.stopListening();
  }, []);

  const simulateTranscription = useCallback((text: string) => {
    log.debug("audio", `🎭 Simulando transcripción: ${text.substring(0, 50)}`);
    transcriptionService.simulateTranscription(text);
  }, []);

  const clearTranscription = useCallback(() => {
    log.debug("audio", "🧹 Limpiando transcripción");
    setTranscription(null);
    transcriptionService.clearTranscription();
  }, []);

  // ========== FUNCIONES OUTPUT ==========
  const configVoice = useCallback(async (genre: string): Promise<boolean> => {
    try {
      setIsConfiguring(true);
      log.info("audio", `🔧 Configurando voz: ${genre}`);

      const configured = await speechService.configVoice(genre);

      if (configured) {
        setIsLocutionActivated(true);
        setIsAudioOnPlay(false);

        // Actualizar voces disponibles
        const voices = speechService.getAvailableVoicesList();
        setAvailableVoices(voices);
        setCurrentVoiceId(speechService.getCurrentVoiceId());

        log.success("audio", "✅ Voz configurada correctamente");
      } else {
        setIsAudioOnPlay(null);
        setIsLocutionActivated(false);
        log.error("audio", "❌ Error configurando voz");
      }

      return configured;
    } catch (error) {
      log.error("audio", "❌ Error configurando voz", error);
      setIsAudioOnPlay(null);
      setIsLocutionActivated(false);
      return false;
    } finally {
      setIsConfiguring(false);
    }
  }, []);

  const getSpeech = useCallback(async (message: string): Promise<string> => {
    try {
      if (!isLocutionActivated) {
        throw new Error("Voz no configurada. Ejecuta configVoice() primero.");
      }
      return await speechService.getSpeech(message);
    } catch (error) {
      log.error("audio", "❌ Error obteniendo speech", error);
      setIsAudioOnPlay(null);
      throw error;
    }
  }, [isLocutionActivated]);

  const setSpeech = useCallback((url: string) => {
    speechService.setSpeech(url);
    setIsAudioOnPlay(false);
  }, []);

  const toSpeech = useCallback(async (url: string) => {
    try {
      await speechService.toSpeech(url);
      setIsAudioOnPlay(true);
    } catch (error) {
      log.error("audio", "❌ Error en toSpeech", error);
      setIsAudioOnPlay(null);
    }
  }, []);

  const speak = useCallback(async (text: string): Promise<void> => {
    log.info("audio", `🎤 Hablando: ${text.substring(0, 50)}`);

    try {
      let voiceConfigured = isLocutionActivated;

      if (!voiceConfigured) {
        log.warn("audio", "⚠️ Voz no configurada, configurando automáticamente...");
        voiceConfigured = await configVoice("female");
        if (!voiceConfigured) {
          throw new Error("No se pudo configurar la voz automáticamente");
        }
      }

      // Usar speechService directamente para evitar problemas de estado asíncrono
      const audioUrl = await speechService.getSpeech(text);
      log.success("audio", "✅ Audio obtenido, reproduciendo...");
      await toSpeech(audioUrl);
    } catch (error) {
      log.error("audio", "❌ Error en speak()", error);
      throw error;
    }
  }, [isLocutionActivated, configVoice, toSpeech]);

  const speakWithAutoConfig = useCallback(async (text: string, genre: string = "female"): Promise<void> => {
    try {
      let voiceConfigured = isLocutionActivated;

      if (!voiceConfigured) {
        log.info("audio", `🔧 Configurando voz: ${genre}`);
        voiceConfigured = await configVoice(genre);
        if (!voiceConfigured) {
          throw new Error(`No se pudo configurar la voz con género: ${genre}`);
        }
      }

      await speak(text);
    } catch (error) {
      log.error("audio", "❌ Error en speakWithAutoConfig()", error);
      throw error;
    }
  }, [isLocutionActivated, configVoice, speak]);

  const onClickSoundBtn = useCallback(() => {
    setIsAudioOnPlay(prev => prev === null ? false : !prev);
  }, []);

  // ========== FUNCIONES MHC ==========
  const setSucwTimeout = useCallback((timeMS: number) => {
    transcriptionService.setSucwTimeout(timeMS);
  }, []);

  const closeWebView = useCallback(() => {
    transcriptionService.closeWebView();
  }, []);

  const hideAura = useCallback(() => {
    transcriptionService.hideAura();
  }, []);

  const speakAura = useCallback((text: string) => {
    transcriptionService.speakAura(text);
  }, []);

  const sendAura = useCallback((text: string) => {
    transcriptionService.sendAura(text);
  }, []);

  const getId = useCallback((): string => {
    return transcriptionService.getId();
  }, []);

  const getStatus = useCallback((): object => {
    return transcriptionService.getStatus();
  }, []);

  // ========== UTILIDADES ==========
  const reset = useCallback(() => {
    log.info("audio", "🔄 Reseteando AudioContext");

    // Reset input
    clearTranscription();
    transcriptionService.reset();

    // Reset output
    setIsLocutionActivated(false);
    setIsAudioOnPlay(null);
    setIsConfiguring(false);
    setAvailableVoices([]);
    setCurrentVoiceId("");
    // speechService.reset(); // Método no disponible en el servicio

    log.success("audio", "✅ AudioContext reseteado");
  }, [clearTranscription]);

  const addEventListener = useCallback((callback: (event: TranscriptionEvent) => void) => {
    return transcriptionService.addEventListener(callback);
  }, []);

  return (
    <AudioContext.Provider
      value={{
        // ===== INPUT (Transcription) =====
        input: {
          transcription,
          isListening: mhcState.isListening,
          isProcessing,
        },

        // Métodos de control de entrada
        startListening,
        stopListening,
        simulateTranscription,
        clearTranscription,

        // ===== OUTPUT (Speech) =====
        output: {
          isLocutionActivated,
          isAudioOnPlay,
          isConfiguring,
          availableVoices,
          currentVoiceId,
        },

        // Métodos de control de salida
        configVoice,
        speak,
        speakWithAutoConfig,
        getSpeech,
        toSpeech,
        setSpeech,
        onClickSoundBtn,

        // ===== MHC Integration =====
        mhc: {
          ...mhcState.mhcInstance,
          isAvailable: mhcState.isMHCAvailable,
          id: mhcState.mhcId,
          status: mhcState.mhcStatus,
        },

        // Métodos MHC
        setSucwTimeout,
        closeWebView,
        hideAura,
        speakAura,
        sendAura,
        getId,
        isMHCAvailable: () => mhcState.isMHCAvailable,
        getStatus,

        // ===== UTILIDADES =====
        normalize,
        reset,

        // Eventos
        addEventListener,
      }}
    >
      {children}
    </AudioContext.Provider>
  );
};

// ========== EXPORT UTILITIES ==========
export { normalize, MHC };
export type { IMHC, TranscriptionEvent };
