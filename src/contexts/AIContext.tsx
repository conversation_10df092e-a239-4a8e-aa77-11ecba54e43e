import { createContext, useContext, useState, useCallback, type ReactNode } from "react";
import {
  aiService,
  type GameMode,
  type AIResponse,
} from "../services/AIService";
import { log } from "../services/LogService";

// ========== INTERFACES ==========
interface AIContextProps {
  // Estado de la IA
  isLoading: boolean;
  sessionId: string | null;

  // Configuración
  currentPreset: string;
  getPresetForMode: (mode: GameMode) => string;
  getApiConfig: () => any;

  // Métodos principales
  generateResponse: (query: string, mode: GameMode) => Promise<AIResponse>;
  resetSession: () => Promise<void>;

  // Utilidades
  isConfigured: () => boolean;
  getSessionStatus: () => {
    sessionId: string | null;
    isActive: boolean;
    preset: string;
  };
}

// ========== CONTEXT ==========
const AIContext = createContext<AIContextProps | undefined>(undefined);

export const useAIContext = () => {
  const context = useContext(AIContext);
  if (!context) {
    throw new Error("useAIContext must be used within an AIProvider");
  }
  return context;
};

// Mantener compatibilidad con el nombre anterior
export const useIAContext = useAIContext;

export const AIProvider = ({ children }: { children: ReactNode }) => {
  // ========== ESTADOS ==========
  const [isLoading, setIsLoading] = useState<boolean>(false);

  // ========== COMPUTED VALUES ==========
  const sessionId = aiService.getSessionId();
  const currentPreset = aiService.getApiConfig().presets.user;

  // ========== FUNCTIONS ==========
  const generateResponse = useCallback(async (
    query: string,
    mode: GameMode,
  ): Promise<AIResponse> => {
    setIsLoading(true);

    try {
      log.info("ai", `🤖 Generando respuesta para modo: ${mode}`, {
        query: query.substring(0, 50),
        sessionId,
      });

      const response = await aiService.generateResponse(query, mode);

      log.success("ai", "✅ Respuesta generada exitosamente", {
        outputLength: response.output?.length,
        sessionId: response.sessionId,
      });

      return response;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Error desconocido";
      log.error("ai", "❌ Error generando respuesta", {
        error: errorMessage,
        query: query.substring(0, 50),
        mode,
      });
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [sessionId]);

  const resetSession = useCallback(async (): Promise<void> => {
    try {
      log.info("ai", "🔄 Reseteando sesión de IA");
      await aiService.resetSession();
      log.success("ai", "✅ Sesión reseteada correctamente");
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Error desconocido";
      log.error("ai", "❌ Error al resetear sesión", { error: errorMessage });
      throw error;
    }
  }, []);

  // ========== UTILIDADES ==========
  const getPresetForMode = useCallback((mode: GameMode): string => {
    return aiService.getPresetForMode(mode);
  }, []);

  const getApiConfig = useCallback(() => {
    return aiService.getApiConfig();
  }, []);

  const isConfigured = useCallback((): boolean => {
    const config = aiService.getApiConfig();
    return !!(config.baseURL && config.apiKey);
  }, []);

  const getSessionStatus = useCallback(() => {
    return {
      sessionId,
      isActive: !!sessionId,
      preset: currentPreset,
    };
  }, [sessionId, currentPreset]);

  return (
    <AIContext.Provider
      value={{
        // Estado de la IA
        isLoading,
        sessionId,

        // Configuración
        currentPreset,
        getPresetForMode,
        getApiConfig,

        // Métodos principales
        generateResponse,
        resetSession,

        // Utilidades
        isConfigured,
        getSessionStatus,
      }}
    >
      {children}
    </AIContext.Provider>
  );
};

// ========== EXPORT TYPES ==========
export type { GameMode };
