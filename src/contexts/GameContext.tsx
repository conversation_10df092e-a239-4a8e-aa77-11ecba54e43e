import {
  createContext,
  useContext,
  useState,
  useCallback,
  type ReactNode,
} from "react";
import { log } from "../services/LogService";

// ========== TYPES ==========
export type GameMode = "yo_pienso" | "aura_piensa";
export type GameState = "waiting" | "playing" | "guessing" | "finished";

export interface GameMessage {
  text: string;
  isUser: boolean;
  timestamp: Date;
}

export interface GameSession {
  id: string;
  mode: GameMode;
  startTime: Date;
  endTime?: Date;
  messages: GameMessage[];
  questionCount: number;
  isWin?: boolean;
  characterName?: string;
  finalMessage?: string;
}

// ========== INTERFACES ==========
interface GameContextProps {
  // Estado del juego
  gameState: GameState;
  gameMode: GameMode | null;
  questionCount: number;

  // Sesión actual
  currentSession: GameSession | null;
  messages: GameMessage[];

  // Métodos de control del juego
  startGame: (mode: GameMode) => void;
  endGame: (isWin?: boolean, characterName?: string, finalMessage?: string) => void;
  pauseGame: () => void;
  resumeGame: () => void;
  resetGame: () => void;

  // Métodos de mensajes
  addMessage: (text: string, isUser: boolean) => void;
  clearMessages: () => void;

  // Métodos de estado
  setGameState: (state: GameState) => void;
  incrementQuestionCount: () => void;
  resetQuestionCount: () => void;

  // Utilidades
  getGameDuration: () => number | null;
  getGameStats: () => {
    totalQuestions: number;
    duration: number | null;
    messagesCount: number;
  };

  // Historial de sesiones
  sessionHistory: GameSession[];
  getSessionHistory: () => GameSession[];
  clearSessionHistory: () => void;
}

// ========== CONTEXT ==========
const GameContext = createContext<GameContextProps | undefined>(undefined);

export const useGameContext = () => {
  const context = useContext(GameContext);
  if (!context) {
    throw new Error("useGameContext must be used within a GameProvider");
  }
  return context;
};

export const GameProvider = ({ children }: { children: ReactNode }) => {
  // ========== ESTADOS ==========
  const [gameState, setGameState] = useState<GameState>("waiting");
  const [gameMode, setGameMode] = useState<GameMode | null>(null);
  const [questionCount, setQuestionCount] = useState<number>(0);
  const [currentSession, setCurrentSession] = useState<GameSession | null>(null);
  const [messages, setMessages] = useState<GameMessage[]>([]);
  const [sessionHistory, setSessionHistory] = useState<GameSession[]>([]);

  // ========== FUNCIONES DE CONTROL DEL JUEGO ==========
  const startGame = useCallback((mode: GameMode) => {
    log.info("game", `🎮 Iniciando juego en modo: ${mode}`);

    const newSession: GameSession = {
      id: `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      mode,
      startTime: new Date(),
      messages: [],
      questionCount: 0,
    };

    setGameMode(mode);
    setGameState("playing");
    setQuestionCount(0);
    setMessages([]);
    setCurrentSession(newSession);

    log.success("game", `✅ Juego iniciado - Sesión: ${newSession.id}`);
  }, []);

  const endGame = useCallback((isWin?: boolean, characterName?: string, finalMessage?: string) => {
    if (!currentSession) {
      log.warn("game", "⚠️ Intentando finalizar juego sin sesión activa");
      return;
    }

    log.info("game", "🏁 Finalizando juego", {
      sessionId: currentSession.id,
      isWin,
      characterName,
      questionCount,
    });

    const completedSession: GameSession = {
      ...currentSession,
      endTime: new Date(),
      messages: [...messages],
      questionCount,
      isWin,
      characterName,
      finalMessage,
    };

    // Agregar al historial
    setSessionHistory(prev => [...prev, completedSession]);

    // Resetear estado
    setGameState("finished");
    setCurrentSession(null);

    log.success("game", `✅ Juego finalizado - Duración: ${getGameDuration()}ms`);
  }, [currentSession, messages, questionCount]);

  const pauseGame = useCallback(() => {
    if (gameState === "playing") {
      setGameState("waiting");
      log.info("game", "⏸️ Juego pausado");
    }
  }, [gameState]);

  const resumeGame = useCallback(() => {
    if (gameState === "waiting" && currentSession) {
      setGameState("playing");
      log.info("game", "▶️ Juego reanudado");
    }
  }, [gameState, currentSession]);

  const resetGame = useCallback(() => {
    log.info("game", "🔄 Reseteando juego");

    setGameState("waiting");
    setGameMode(null);
    setQuestionCount(0);
    setMessages([]);
    setCurrentSession(null);

    log.success("game", "✅ Juego reseteado");
  }, []);

  // ========== FUNCIONES DE MENSAJES ==========
  const addMessage = useCallback((text: string, isUser: boolean) => {
    const message: GameMessage = {
      text,
      isUser,
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, message]);

    // Incrementar contador de preguntas solo para mensajes del usuario
    if (isUser && gameState === "playing") {
      setQuestionCount(prev => prev + 1);
    }

    log.debug("game", `💬 Mensaje agregado: ${isUser ? "Usuario" : "IA"}`, {
      text: text.substring(0, 50),
      questionCount: isUser ? questionCount + 1 : questionCount,
    });
  }, [gameState, questionCount]);

  const clearMessages = useCallback(() => {
    setMessages([]);
    log.debug("game", "🧹 Mensajes limpiados");
  }, []);

  // ========== FUNCIONES DE ESTADO ==========
  const incrementQuestionCount = useCallback(() => {
    setQuestionCount(prev => prev + 1);
    log.debug("game", `📊 Contador de preguntas: ${questionCount + 1}`);
  }, [questionCount]);

  const resetQuestionCount = useCallback(() => {
    setQuestionCount(0);
    log.debug("game", "🔄 Contador de preguntas reseteado");
  }, []);

  // ========== UTILIDADES ==========
  const getGameDuration = useCallback((): number | null => {
    if (!currentSession) return null;

    const endTime = currentSession.endTime || new Date();
    return endTime.getTime() - currentSession.startTime.getTime();
  }, [currentSession]);

  const getGameStats = useCallback(() => {
    return {
      totalQuestions: questionCount,
      duration: getGameDuration(),
      messagesCount: messages.length,
    };
  }, [questionCount, getGameDuration, messages.length]);

  const getSessionHistory = useCallback(() => {
    return [...sessionHistory].sort((a, b) => b.startTime.getTime() - a.startTime.getTime());
  }, [sessionHistory]);

  const clearSessionHistory = useCallback(() => {
    setSessionHistory([]);
    log.info("game", "🧹 Historial de sesiones limpiado");
  }, []);

  return (
    <GameContext.Provider
      value={{
        // Estado del juego
        gameState,
        gameMode,
        questionCount,

        // Sesión actual
        currentSession,
        messages,

        // Métodos de control del juego
        startGame,
        endGame,
        pauseGame,
        resumeGame,
        resetGame,

        // Métodos de mensajes
        addMessage,
        clearMessages,

        // Métodos de estado
        setGameState,
        incrementQuestionCount,
        resetQuestionCount,

        // Utilidades
        getGameDuration,
        getGameStats,

        // Historial de sesiones
        sessionHistory,
        getSessionHistory,
        clearSessionHistory,
      }}
    >
      {children}
    </GameContext.Provider>
  );
};

// ========== EXPORT TYPES ==========
// Los tipos ya están exportados arriba con export interface/type
