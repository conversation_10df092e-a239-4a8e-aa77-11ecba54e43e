// contexts/SpeechInputContext.tsx
import {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  type ReactNode,
} from "react";
import {
  transcriptionService,
  type TranscriptionEvent,
  normalize,
} from "../services/TranscriptionService";
import { log } from "../services/LogService";

// ========== TYPES ==========
export type GameResponseType = "yes" | "no" | "maybe" | "unknown" | "invalid";

export interface SpeechInputState {
  transcription: string | null;
  isListening: boolean;
  isProcessing: boolean;
  confidence: number;
  lastValidatedResponse: GameResponseType | null;
  errorMessage: string | null;
}

export interface SpeechInputContextProps {
  // Current state
  state: SpeechInputState;

  // Basic controls
  startListening: () => void;
  stopListening: () => void;
  clearTranscription: () => void;
  reset: () => void;

  // Testing & simulation
  simulateTranscription: (text: string) => void;

  // Game-specific functionality
  validateGameResponse: (text: string) => GameResponseType;
  waitForValidResponse: (timeout?: number) => Promise<GameResponseType>;
  waitForCustomResponse: (expectedResponses: string[], timeout?: number) => Promise<string>;

  // Event handling
  addEventListener: (callback: (event: TranscriptionEvent) => void) => () => void;

  // Utilities
  normalizeText: (text: string) => Promise<string>;
  getSupportedResponses: () => string[];
  getResponseHelp: () => string;
}

// ========== CONTEXT ==========
const SpeechInputContext = createContext<SpeechInputContextProps | undefined>(undefined);

export const useSpeechInput = () => {
  const context = useContext(SpeechInputContext);
  if (!context) {
    throw new Error("useSpeechInput must be used within SpeechInputProvider");
  }
  return context;
};

// ========== PROVIDER ==========
export const SpeechInputProvider = ({ children }: { children: ReactNode }) => {
  // ========== LOCAL STATE ==========
  const [state, setState] = useState<SpeechInputState>({
    transcription: null,
    isListening: false,
    isProcessing: false,
    confidence: 0,
    lastValidatedResponse: null,
    errorMessage: null,
  });

  // ========== GAME RESPONSE PATTERNS ==========
  const RESPONSE_PATTERNS = {
    yes: [
      "sí", "si", "yes", "correcto", "afirmativo", "exacto", "cierto",
      "verdad", "por supuesto", "claro", "efectivamente", "así es"
    ],
    no: [
      "no", "nope", "negativo", "incorrecto", "falso", "para nada",
      "jamás", "nunca", "de ninguna manera", "en absoluto"
    ],
    maybe: [
      "tal vez", "quizás", "puede ser", "posible", "posiblemente",
      "probablemente", "es posible", "podría ser", "a veces", "depende"
    ],
    unknown: [
      "no sé", "no lo sé", "no tengo idea", "desconozco", "ni idea",
      "no estoy seguro", "no sabría decir", "no tengo ni idea"
    ]
  };

  // ========== EFFECTS ==========
  useEffect(() => {
    log.info("speechInput", "🎤 Inicializando SpeechInputProvider");

    // Listener principal para eventos de transcripción
    const removeListener = transcriptionService.addEventListener((event: TranscriptionEvent) => {
      log.debug("speechInput", `📝 Evento recibido: ${event.type}`, {
        data: event.data.substring(0, 50),
      });

      switch (event.type) {
        case "transcription":
          setState(prev => ({
            ...prev,
            transcription: event.normalized || event.data,
            isProcessing: false,
            errorMessage: null,
            lastValidatedResponse: validateGameResponse(event.normalized || event.data)
          }));
          break;

        case "error":
          setState(prev => ({
            ...prev,
            isProcessing: false,
            errorMessage: event.data,
          }));
          break;

        case "command":
          log.info("speechInput", `🎯 Comando ejecutado: ${event.data}`);
          break;
      }
    });

    // Sincronizar estado de listening
    const updateListeningState = () => {
      const isCurrentlyListening = transcriptionService.isCurrentlyListening();
      setState(prev => ({
        ...prev,
        isListening: isCurrentlyListening
      }));
    };

    // Actualizar estado inicial
    updateListeningState();

    // Polling para mantener sincronizado el estado (backup)
    const pollInterval = setInterval(updateListeningState, 1000);

    return () => {
      log.info("speechInput", "🧹 Limpiando SpeechInputProvider");
      removeListener();
      clearInterval(pollInterval);
    };
  }, []);

  // ========== BASIC CONTROLS ==========
  const startListening = useCallback(() => {
    log.info("speechInput", "🎤 Iniciando escucha");

    setState(prev => ({ ...prev, isProcessing: true, errorMessage: null }));

    try {
      transcriptionService.startListening();
      setState(prev => ({ ...prev, isListening: true, isProcessing: false }));
    } catch (error) {
      log.error("speechInput", "❌ Error iniciando escucha", error);
      setState(prev => ({
        ...prev,
        isProcessing: false,
        errorMessage: "Error al iniciar el micrófono"
      }));
    }
  }, []);

  const stopListening = useCallback(() => {
    log.info("speechInput", "🛑 Deteniendo escucha");

    try {
      transcriptionService.stopListening();
      setState(prev => ({ ...prev, isListening: false }));
    } catch (error) {
      log.error("speechInput", "❌ Error deteniendo escucha", error);
    }
  }, []);

  const clearTranscription = useCallback(() => {
    log.debug("speechInput", "🧹 Limpiando transcripción");

    transcriptionService.clearTranscription();
    setState(prev => ({
      ...prev,
      transcription: null,
      lastValidatedResponse: null,
      errorMessage: null
    }));
  }, []);

  const reset = useCallback(() => {
    log.info("speechInput", "🔄 Reseteando SpeechInput");

    transcriptionService.reset();
    setState({
      transcription: null,
      isListening: false,
      isProcessing: false,
      confidence: 0,
      lastValidatedResponse: null,
      errorMessage: null,
    });
  }, []);

  // ========== TESTING & SIMULATION ==========
  const simulateTranscription = useCallback((text: string) => {
    log.debug("speechInput", `🎭 Simulando transcripción: ${text}`);
    transcriptionService.simulateTranscription(text);
  }, []);

  // ========== GAME-SPECIFIC FUNCTIONALITY ==========
  const validateGameResponse = useCallback((text: string): GameResponseType => {
    if (!text || !text.trim()) return "invalid";

    const normalized = text.toLowerCase().trim();
    log.debug("speechInput", `🔍 Validando respuesta: "${text}" → "${normalized}"`);

    // Verificar cada categoría
    for (const [responseType, patterns] of Object.entries(RESPONSE_PATTERNS)) {
      for (const pattern of patterns) {
        if (normalized.includes(pattern)) {
          log.debug("speechInput", `✅ Respuesta válida: ${responseType}`, { pattern });
          return responseType as GameResponseType;
        }
      }
    }

    log.debug("speechInput", `❌ Respuesta inválida: ${normalized}`);
    return "invalid";
  }, []);

  const waitForValidResponse = useCallback((timeout: number = 30000): Promise<GameResponseType> => {
    return new Promise((resolve, reject) => {
      log.info("speechInput", `⏳ Esperando respuesta válida (timeout: ${timeout}ms)`);

      let timeoutId: NodeJS.Timeout;
      let resolved = false;

      const handleTranscription = (event: TranscriptionEvent) => {
        if (resolved || event.type !== "transcription") return;

        const validated = validateGameResponse(event.normalized || event.data);

        if (validated !== "invalid") {
          resolved = true;
          clearTimeout(timeoutId);
          removeListener();

          log.success("speechInput", `✅ Respuesta válida recibida: ${validated}`);
          resolve(validated);
        } else {
          log.debug("speechInput", `⚠️ Respuesta inválida ignorada: ${event.data}`);
        }
      };

      const removeListener = transcriptionService.addEventListener(handleTranscription);

      timeoutId = setTimeout(() => {
        if (!resolved) {
          resolved = true;
          removeListener();
          log.warn("speechInput", "⏰ Timeout esperando respuesta válida");
          reject(new Error("Timeout esperando respuesta válida"));
        }
      }, timeout);
    });
  }, [validateGameResponse]);

  const waitForCustomResponse = useCallback((
    expectedResponses: string[],
    timeout: number = 30000
  ): Promise<string> => {
    return new Promise((resolve, reject) => {
      log.info("speechInput", `⏳ Esperando respuestas específicas`, { expectedResponses });

      let timeoutId: NodeJS.Timeout;
      let resolved = false;

      const handleTranscription = (event: TranscriptionEvent) => {
        if (resolved || event.type !== "transcription") return;

        const normalized = event.normalized || event.data;
        const matchesExpected = expectedResponses.some(expected =>
          normalized.toLowerCase().includes(expected.toLowerCase())
        );

        if (matchesExpected || expectedResponses.length === 0) {
          resolved = true;
          clearTimeout(timeoutId);
          removeListener();

          log.success("speechInput", `✅ Respuesta esperada recibida: ${normalized}`);
          resolve(normalized);
        } else {
          log.debug("speechInput", `⚠️ Respuesta no esperada: ${normalized}`);
        }
      };

      const removeListener = transcriptionService.addEventListener(handleTranscription);

      timeoutId = setTimeout(() => {
        if (!resolved) {
          resolved = true;
          removeListener();
          log.warn("speechInput", "⏰ Timeout esperando respuesta específica");
          reject(new Error("Timeout esperando respuesta específica"));
        }
      }, timeout);
    });
  }, []);

  // ========== EVENT HANDLING ==========
  const addEventListener = useCallback((callback: (event: TranscriptionEvent) => void) => {
    return transcriptionService.addEventListener(callback);
  }, []);

  // ========== UTILITIES ==========
  const normalizeText = useCallback(async (text: string): Promise<string> => {
    return await normalize(text);
  }, []);

  const getSupportedResponses = useCallback((): string[] => {
    return [
      "Sí / No",
      "Tal vez / Quizás",
      "No lo sé / No sé",
      "Es posible / Puede ser"
    ];
  }, []);

  const getResponseHelp = useCallback((): string => {
    return "Puedes responder con: Sí, No, Tal vez, No lo sé, o sus variaciones";
  }, []);

  // ========== CONTEXT VALUE ==========
  const contextValue: SpeechInputContextProps = {
    // Current state
    state,

    // Basic controls
    startListening,
    stopListening,
    clearTranscription,
    reset,

    // Testing & simulation
    simulateTranscription,

    // Game-specific functionality
    validateGameResponse,
    waitForValidResponse,
    waitForCustomResponse,

    // Event handling
    addEventListener,

    // Utilities
    normalizeText,
    getSupportedResponses,
    getResponseHelp,
  };

  return (
    <SpeechInputContext.Provider value={contextValue}>
      {children}
    </SpeechInputContext.Provider>
  );
};
