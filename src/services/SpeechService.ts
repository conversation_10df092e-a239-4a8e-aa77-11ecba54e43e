import axios from "axios";
import { log } from "./LogService";

// ========== INTERFACES ==========
interface IVoicesService {
  getAvailableVoices(): Promise<string[]>;
  getAudio(text: string): Promise<any>;
  configVoice(genre: string): Promise<boolean>;
  getCurrentVoiceId(): string;
  getAvailableVoicesList(): string[];
  reset(): void;
}

// ========== CONFIGURACIÓN API ==========
const LANGUAGE = "es";
const BASE_URL = import.meta.env.VITE_SPEECH_API_URL;
const API_KEY = import.meta.env.VITE_SPEECH_API_KEY;

// ========== AZURE VOICES SERVICE ==========
class AzureVoicesService implements IVoicesService {
  private static instance: AzureVoicesService;
  private voiceId: string = "";
  private genre: string = "";
  private availableVoicesList: string[] = [];

  private constructor() {}

  public static getInstance(): AzureVoicesService {
    if (!AzureVoicesService.instance) {
      AzureVoicesService.instance = new AzureVoicesService();
    }
    return AzureVoicesService.instance;
  }

  private setVoiceId(voice: string) {
    this.voiceId = voice;
  }

  private setGenre(genre: string) {
    this.genre = genre;
  }

  public getCurrentVoiceId(): string {
    return this.voiceId;
  }

  public getAvailableVoicesList(): string[] {
    return this.availableVoicesList;
  }

  public isConfigured(): boolean {
    return Boolean(this.getCurrentVoiceId());
  }

  public getStats(): object {
    return {
      configured: this.isConfigured(),
      currentVoiceId: this.getCurrentVoiceId(),
      availableVoices: this.getAvailableVoicesList().length,
      voicesList: this.getAvailableVoicesList(),
      audioElementReady: false, // Placeholder value since the method is not defined
      apiUrl: BASE_URL,
      apiKeyConfigured: Boolean(API_KEY),
      language: LANGUAGE,
      timestamp: new Date().toISOString(),
    };
  }

  public reset(): void {
    console.log("🔄 AzureVoicesService: Reseteando...");
    this.voiceId = "";
    this.genre = "";
    this.availableVoicesList = [];
  }

  public configVoice(genre: string): Promise<boolean> {
    this.setGenre(genre);

    return this.getAvailableVoices()
      .then((voices: string[]) => {
        this.availableVoicesList = voices;
        if (voices.length > 0) {
          const randomVoice = voices[Math.floor(Math.random() * voices.length)];
          this.setVoiceId(randomVoice);
          return true;
        }
        return false;
      })
      .catch((error) => {
        console.error("Error fetching voices:", error);
        this.availableVoicesList = [];
        return false;
      });
  }

  private async handleRequest<T>(request: Promise<any>): Promise<T> {
    try {
      const res = await request;
      return res.data as T;
    } catch (error) {
      if (import.meta.env.MODE === "development") {
        if (error instanceof Error) {
          console.error(`🔴 Azure API ERROR: ${error.message}`);
        } else {
          console.error("🔴 Azure API ERROR: Unknown error", error);
        }
      }
      throw new Error("Error al conectar con Azure Speech API");
    }
  }

  private getHeaders(): Record<string, string> {
    if (!API_KEY) {
      throw new Error("VITE_SPEECH_API_KEY no está configurada");
    }
    return {
      Authorization: `Bearer ${API_KEY}`,
      "Content-Type": "application/json",
    };
  }

  getAvailableVoices(): Promise<string[]> {
    if (!BASE_URL) {
      throw new Error("VITE_SPEECH_API_URL no está configurada");
    }

    const data = {
      language: LANGUAGE,
      gender: this.genre,
    };

    return this.handleRequest<string[]>(
      axios.post(`${BASE_URL}available_voices`, data, {
        headers: this.getHeaders(),
      }),
    );
  }

  getAudio(text: string): Promise<Blob> {
    if (!BASE_URL) {
      throw new Error("VITE_SPEECH_API_URL no está configurada");
    }

    if (!this.voiceId) {
      throw new Error("No hay voz configurada. Ejecuta configVoice() primero.");
    }

    return this.handleRequest<Blob>(
      axios.post(
        `${BASE_URL}t2s`,
        {
          input_text: text,
          voice_params: {
            voice_id: this.voiceId,
            rate: 1.1,
          },
          output_format: "mp3",
        },
        {
          responseType: "blob",
          headers: this.getHeaders(),
        },
      ),
    );
  }
}

// ========== SPEECH SERVICE PRINCIPAL ==========
class SpeechService {
  private azureService: IVoicesService;
  private audioElement: HTMLAudioElement | null = null;
  private serviceName = "speech";
  private userHasInteracted = false;

  constructor() {
    this.azureService = AzureVoicesService.getInstance();
    this.initializeAudioElement();
    this.setupUserInteractionDetection();
  }

  private initializeAudioElement() {
    // Buscar elemento de audio existente o crearlo
    this.audioElement = document.getElementById("audio") as HTMLAudioElement;
    if (!this.audioElement) {
      this.audioElement = document.createElement("audio");
      this.audioElement.id = "audio";
      this.audioElement.preload = "metadata";
      document.body.appendChild(this.audioElement);
    }
  }

  private setupUserInteractionDetection() {
    // Detectar primera interacción del usuario
    const events = ['click', 'touchstart', 'keydown'];

    const handleFirstInteraction = () => {
      this.userHasInteracted = true;
      log.info(this.serviceName, "✅ Primera interacción del usuario detectada");

      // Remover listeners después de la primera interacción
      events.forEach(event => {
        document.removeEventListener(event, handleFirstInteraction);
      });
    };

    events.forEach(event => {
      document.addEventListener(event, handleFirstInteraction, { once: true });
    });
  }

  private getAudioElement(): HTMLAudioElement {
    if (!this.audioElement) {
      this.initializeAudioElement();
    }
    return this.audioElement!;
  }

  // ========== MÉTODOS DE AUDIO ==========
  async playSpeech(): Promise<void> {
    const audioElement = this.getAudioElement();
    audioElement.volume = 1;

    try {
      await audioElement.play();
      log.debug(this.serviceName, "✅ Audio reproducido exitosamente");
    } catch (error) {
      if (error instanceof Error && error.name === 'NotAllowedError') {
        log.warn(this.serviceName, "⚠️ Autoplay bloqueado - se requiere interacción del usuario");
        this.handleAutoplayBlocked();
      } else {
        log.error(this.serviceName, "❌ Error reproduciendo audio", error);
        throw error;
      }
    }
  }

  private handleAutoplayBlocked(): void {
    if (!this.userHasInteracted) {
      log.info(this.serviceName, "🔔 Esperando interacción del usuario para reproducir audio");

      // Mostrar notificación visual (opcional)
      this.showAudioPermissionNotification();

      // Intentar reproducir cuando el usuario interactúe
      const playOnInteraction = async () => {
        try {
          const audioElement = this.getAudioElement();
          if (audioElement.src && audioElement.readyState >= 2) {
            await audioElement.play();
            log.success(this.serviceName, "✅ Audio reproducido después de interacción");
          }
        } catch (error) {
          log.error(this.serviceName, "❌ Error reproduciendo audio tras interacción", error);
        }
      };

      // Escuchar próxima interacción
      const events = ['click', 'touchstart', 'keydown'];
      events.forEach(event => {
        document.addEventListener(event, playOnInteraction, { once: true });
      });
    }
  }

  private showAudioPermissionNotification(): void {
    // Solo mostrar en desarrollo para no molestar al usuario
    if (import.meta.env.MODE === "development") {
      console.log("🔔 Para escuchar el audio, haz clic en cualquier parte de la página");
    }
  }

  stopSpeech(): void {
    const audioElement = this.getAudioElement();
    audioElement.volume = 0;
    audioElement.pause();
  }

  noSpeech(): void {
    this.stopSpeech();
    const audioElement = this.getAudioElement();
    audioElement.onended = null;
    audioElement.src = "";
  }

  setSpeech(url: string): void {
    const audioElement = this.getAudioElement();
    audioElement.onended = null;
    audioElement.src = url;
    audioElement.currentTime = 0;
    audioElement.volume = 1;
  }

  async toSpeech(url: string): Promise<void> {
    const audioElement = this.getAudioElement();
    audioElement.onended = null;
    audioElement.src = url;
    audioElement.currentTime = 0;
    audioElement.volume = 1;

    try {
      await audioElement.play();
      log.debug(this.serviceName, "✅ Audio reproducido exitosamente");
    } catch (error) {
      if (error instanceof Error && error.name === 'NotAllowedError') {
        log.warn(this.serviceName, "⚠️ Autoplay bloqueado en toSpeech - se requiere interacción del usuario");
        this.handleAutoplayBlocked();
      } else {
        log.error(this.serviceName, "❌ Error en toSpeech", error);
        throw error;
      }
    }
  }

  // ========== MÉTODOS DE VOZ ==========
  async configVoice(genre: string): Promise<boolean> {
    log.info(this.serviceName, `Configurando voz género: ${genre}`);

    try {
      const success = await this.azureService.configVoice(genre);

      if (success) {
        const voiceId = this.azureService.getCurrentVoiceId();
        log.success(
          this.serviceName,
          `Voz configurada exitosamente: ${voiceId}`,
        );
      } else {
        log.warn(this.serviceName, "No se pudo configurar la voz");
      }

      return success;
    } catch (error) {
      log.error(this.serviceName, "Error configurando voz", error);
      return false;
    }
  }

  async getSpeech(message: string): Promise<string> {
    try {
      const audioBlob = await this.azureService.getAudio(message);
      const audioUrl = URL.createObjectURL(audioBlob);
      return audioUrl;
    } catch (error) {
      console.error("❌ SpeechService: Error obteniendo speech:", error);
      throw error;
    }
  }

  getCurrentVoiceId(): string {
    return this.azureService.getCurrentVoiceId();
  }

  getAvailableVoicesList(): string[] {
    return this.azureService.getAvailableVoicesList();
  }

  // ========== MÉTODO PRINCIPAL ==========
  async speak(text: string): Promise<void> {
    log.debug(
      this.serviceName,
      `Iniciando speech: "${text.substring(0, 50)}..."`,
    );

    try {
      const audioUrl = await this.getSpeech(text);
      await this.toSpeech(audioUrl);
      log.success(this.serviceName, "Speech reproducido exitosamente");
    } catch (error) {
      log.error(this.serviceName, "Error en speak()", error);
      throw error;
    }
  }

  // ========== MÉTODO DE AUTO-CONFIGURACIÓN ==========
  async speakWithAutoConfig(
    text: string,
    genre: string = "female",
  ): Promise<void> {
    try {
      // Verificar si ya está configurado
      if (!this.getCurrentVoiceId()) {
        console.log("🔧 SpeechService: Auto-configurando voz...");
        const configured = await this.configVoice(genre);
        if (!configured) {
          throw new Error("No se pudo configurar la voz automáticamente");
        }
      }

      await this.speak(text);
    } catch (error) {
      console.error("❌ SpeechService: Error en speakWithAutoConfig():", error);
      throw error;
    }
  }
}

// ========== INSTANCIA SINGLETON ==========
export const speechService = new SpeechService();
