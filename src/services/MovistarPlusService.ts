import axios from "axios";
import { log } from "./LogService";

// ========== TYPES ==========
export interface FilmData {
  title: string;
  recommendation: string;
}

export interface MovistarPlusResponse {
  success: boolean;
  data?: any;
  error?: string;
  titles?: string[];
}

interface IMovistarPlusService {
  getMovistarPlusFilms(film: FilmData): Promise<MovistarPlusResponse>;
  searchByTitle(title: string): Promise<MovistarPlusResponse>;
  setFilmToLaunch(film: string | null): void;
  getApiConfig(): object;
}

// ========== CONFIGURATION ==========
const API_CONFIG = {
  baseURL: import.meta.env.VITE_MOVISTAR_API_URL || "",
  timeout: 10000, // 10 segundos
  retries: 2,
};

// ========== MOVISTAR PLUS SERVICE ==========
class MovistarPlusService implements IMovistarPlusService {
  private static instance: MovistarPlusService;
  private filmToLaunch: string | null = null;
  private serviceName = "movistar";

  private constructor() {}

  public static getInstance(): MovistarPlusService {
    if (!MovistarPlusService.instance) {
      MovistarPlusService.instance = new MovistarPlusService();
    }
    return MovistarPlusService.instance;
  }

  // ========== GETTERS ==========
  public getFilmToLaunch(): string | null {
    return this.filmToLaunch;
  }

  public getApiConfig(): object {
    return { ...API_CONFIG };
  }

  // ========== PRIVATE METHODS ==========
  private async makeRequest(url: string, retryCount = 0): Promise<any> {
    try {
      if (!API_CONFIG.baseURL) {
        throw new Error("VITE_MOVISTAR_API_URL no está configurada");
      }

      console.log(`🎬 MovistarPlus Service: Realizando petición a ${url}`);

      const response = await axios.get(url, {
        timeout: API_CONFIG.timeout,
        headers: {
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      });

      if (response.status !== 200) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      console.log(`✅ MovistarPlus Service: Respuesta exitosa`);
      return response.data;
    } catch (error) {
      console.error(
        `❌ MovistarPlus Service: Error en petición (intento ${retryCount + 1}):`,
        error,
      );

      // Retry logic
      if (retryCount < API_CONFIG.retries && this.isRetryableError(error)) {
        console.log(
          `🔄 MovistarPlus Service: Reintentando... (${retryCount + 1}/${API_CONFIG.retries})`,
        );
        await this.delay(1000 * (retryCount + 1)); // Exponential backoff
        return this.makeRequest(url, retryCount + 1);
      }

      throw error;
    }
  }

  private isRetryableError(error: any): boolean {
    // Reintentar en caso de errores de red o timeouts
    return (
      error.code === "ECONNABORTED" || // Timeout
      error.code === "ENOTFOUND" || // DNS error
      error.code === "ECONNRESET" || // Connection reset
      (error.response && error.response.status >= 500) // Server errors
    );
  }

  private delay(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  private extractTitles(data: any): string[] {
    try {
      // Intentar extraer títulos de diferentes estructuras de respuesta
      if (Array.isArray(data)) {
        return data
          .map((item) => item.title || item.name || String(item))
          .filter(Boolean);
      }

      if (data.results && Array.isArray(data.results)) {
        return data.results
          .map((item: any) => item.title || item.name || String(item))
          .filter(Boolean);
      }

      if (data.titles && Array.isArray(data.titles)) {
        return data.titles.filter(Boolean);
      }

      if (data.movies && Array.isArray(data.movies)) {
        return data.movies
          .map((movie: any) => movie.title || movie.name)
          .filter(Boolean);
      }

      if (data.series && Array.isArray(data.series)) {
        return data.series
          .map((serie: any) => serie.title || serie.name)
          .filter(Boolean);
      }

      // Si es un string, devolverlo como array
      if (typeof data === "string") {
        return [data];
      }

      // Si tiene una propiedad title directa
      if (data.title || data.name) {
        return [data.title || data.name];
      }

      console.warn(
        "⚠️ MovistarPlus Service: No se pudieron extraer títulos de la respuesta",
      );
      return [];
    } catch (error) {
      console.error(
        "❌ MovistarPlus Service: Error extrayendo títulos:",
        error,
      );
      return [];
    }
  }

  // ========== PUBLIC METHODS ==========
  public async getMovistarPlusFilms(
    film: FilmData,
  ): Promise<MovistarPlusResponse> {
    log.info(this.serviceName, `Buscando contenido: "${film.title}"`);

    try {
      const url = `${API_CONFIG.baseURL}${encodeURIComponent(film.title)}`;
      log.debug(this.serviceName, `URL de búsqueda: ${url}`);

      const data = await this.makeRequest(url);
      // Extraer títulos de la respuesta
      const titles = this.extractTitles(data);

      log.success(
        this.serviceName,
        `Contenido encontrado: ${titles.length} títulos`,
        {
          titles: titles.slice(0, 5), // Solo mostrar primeros 5 en logs
          totalTitles: titles.length,
        },
      );

      return {
        success: true,
        data: data,
        titles: titles,
      };
    } catch (error) {
      log.error(this.serviceName, "Error en getMovistarPlusFilms", error);

      return {
        success: false,
        error: error instanceof Error ? error.message : "Error desconocido",
        data: null,
        titles: [],
      };
    }
  }

  public async searchByTitle(title: string): Promise<MovistarPlusResponse> {
    return this.getMovistarPlusFilms({
      title,
      recommendation: `Búsqueda de ${title}`,
    });
  }

  public setFilmToLaunch(film: string | null): void {
    log.info(
      this.serviceName,
      `Configurando película para lanzar: ${film || "ninguna"}`,
    );

    this.filmToLaunch = film;

    try {
      if (film) {
        localStorage.setItem("movistar_film_to_launch", film);
        log.debug(this.serviceName, "Película guardada en localStorage");
      } else {
        localStorage.removeItem("movistar_film_to_launch");
        log.debug(this.serviceName, "Película removida de localStorage");
      }
    } catch (error) {
      log.warn(this.serviceName, "No se pudo actualizar localStorage", error);
    }
  }

  public reset(): void {
    this.filmToLaunch = null;
    try {
      localStorage.removeItem("movistar_film_to_launch");
    } catch (error) {
      log.warn(this.serviceName, "No se pudo limpiar localStorage", error);
    }
    log.info(this.serviceName, "Servicio reseteado");
  }

  // ========== UTILITY METHODS ==========
  public isConfigured(): boolean {
    return Boolean(API_CONFIG.baseURL);
  }

  public async testConnection(): Promise<boolean> {
    if (!this.isConfigured()) {
      console.warn("⚠️ MovistarPlus Service: API no configurada");
      return false;
    }

    try {
      // Test con una búsqueda simple
      const result = await this.searchByTitle("test");
      return result.success;
    } catch (error) {
      console.error("❌ MovistarPlus Service: Test de conexión falló:", error);
      return false;
    }
  }

  public getStats(): object {
    return {
      configured: this.isConfigured(),
      baseURL: API_CONFIG.baseURL,
      timeout: API_CONFIG.timeout,
      retries: API_CONFIG.retries,
      filmToLaunch: this.filmToLaunch,
    };
  }
}

// ========== SINGLETON EXPORT ==========
export const movistarPlusService = MovistarPlusService.getInstance();
