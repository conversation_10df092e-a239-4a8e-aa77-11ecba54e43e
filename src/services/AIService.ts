import { log } from "./LogService";

// ========== TYPES ==========
export type GameMode = "yo_pienso" | "aura_piensa";

export interface AIResponse {
  ok: boolean;
  output: string;
  sessionId: string;
  error?: string;
}

interface APIPayload {
  id: {
    ses?: string;
    clt: string;
    corr: string;
  };
  preset: string;
  query: string;
  prompt_params: {
    preamble: string;
  };
  model_params: {
    max_tokens: number;
    // temperature?: number;
  };
}

// ========== CONFIGURATION ==========
const API_CONFIG = {
  baseURL:
    import.meta.env.VITE_IA_API_URL ||
    "https://dev.dl2discovery.org/llm-api/v1/",
  apiKey:
    import.meta.env.VITE_IA_API_KEY || "b3df20aa-db3b-49ef-8d3b-4abfac8c1161",
  presets: {
    aura: import.meta.env.VITE_IA_PRESETID_AURA || "mapp-genigma_gpt4o_aura",
    user: import.meta.env.VITE_IA_PRESETID_USER || "mapp-genigma_gpt4o_user",
  },
};

// ========== GAME PROMPTS ==========
const GAME_PROMPTS = {
  yo_pienso: `
        Eres un juego de adivinanza estilo "Akinator". El usuario debe pensar en un personaje, actor, actriz u otra figura del mundo del entretenimiento (real o ficticio).

        Tu tarea es adivinar quién es haciendo preguntas que el usuario solo puede responder con: "Sí", "Es posible", "No", "No sé".

        Reglas del juego:
        - Solo tú haces preguntas. El usuario responde con una de las opciones anteriores.
        - Haz solo una pregunta por turno. No uses preguntas dobles ni abiertas.
        - No repitas preguntas ni hagas variantes de lo que ya sabes.
        - Lleva una lista interna de lo que has aprendido para no preguntar lo mismo.
        - Adapta tus preguntas estratégicamente según las respuestas del usuario.
        - Empieza con preguntas genéricas y así descartas más ideas de golpe.
        - Puedes hacer un máximo de 20 preguntas.
        - Cuando creas tener suficiente información, haz una suposición: "Creo que estás pensando en [nombre del personaje]. ¿Es correcto?"

        Ejemplos de preguntas válidas:
        - ¿Tu personaje aparece en películas?
        - ¿Es una persona real?
        - ¿Es mayor de 50 años?
        - ¿Trabaja principalmente en Hollywood?

        Ejemplos inválidos:
        - ¿Tu personaje es real o ficticio? (pregunta doble)
        - ¿Qué tipo de películas hace? (pregunta abierta)
    `,
  aura_piensa: `
        Eres Aura, una IA que ha pensado en un personaje del mundo del entretenimiento. El usuario debe adivinarlo haciéndote preguntas.

        Tu tarea es:
        - Responder SOLO con "Sí", "No" o "No lo sé" a las preguntas del usuario.
        - Ser consistente con tus respuestas sobre el personaje que elegiste.
        - Mantener el misterio hasta que el usuario adivine correctamente.
        - No dar pistas adicionales, solo responder la pregunta específica.
        - Si el usuario hace una pregunta ambigua, responde "No lo sé".

        IMPORTANTE: Debes "pensar" en un personaje específico al inicio del juego y mantener todas tus respuestas coherentes con ese personaje.

        Ejemplos de respuestas válidas:
        - "Sí"
        - "No"
        - "No lo sé"

        Ejemplos inválidos:
        - "Sí, aparece en muchas películas" (información extra)
        - "No, pero sí en series" (información extra)
        - "Depende de lo que consideres famoso" (explicación)
    `,
};

// ========== AI SERVICE ==========
class AIService {
  private static instance: AIService;
  private sessionId: string | null = null;
  private serviceName = "ai";

  private constructor() {}

  public static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  // ========== GETTERS ==========
  public getSessionId(): string | null {
    return this.sessionId;
  }

  public getPresetForMode(mode: GameMode): string {
    switch (mode) {
      case "yo_pienso":
        return API_CONFIG.presets.user; // Usuario piensa, IA adivina
      case "aura_piensa":
        return API_CONFIG.presets.aura; // Aura piensa, usuario adivina
      default:
        return API_CONFIG.presets.user;
    }
  }

  public getApiConfig() {
    return { ...API_CONFIG };
  }

  // ========== PRIVATE METHODS ==========
  private async makeRequest<T>(endpoint: string, payload?: any): Promise<T> {
    try {
      const url = `${API_CONFIG.baseURL}${endpoint}`;
      const options: RequestInit = {
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": API_CONFIG.apiKey,
        },
      };

      if (payload) {
        options.method = "POST";
        options.body = JSON.stringify(payload);
      } else {
        options.method = "GET";
      }

      const response = await fetch(url, options);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data as T;
    } catch (error) {
      if (import.meta.env.MODE === "development") {
        console.error(`🔴 AI API ERROR: ${error}`);
      }
      throw new Error("Error al conectar con la API de IA");
    }
  }

  private buildPayload(query: string, mode: GameMode): APIPayload {
    const preset = this.getPresetForMode(mode);

    return {
      id: {
        ses: this.sessionId || undefined,
        clt: "aura-game-client",
        corr: `game-${Date.now()}`,
      },
      preset,
      query,
      prompt_params: {
        preamble: GAME_PROMPTS[mode],
      },
      model_params: {
        max_tokens: mode === "aura_piensa" ? 50 : 200,
        // temperature: mode === "aura_piensa" ? 0.3 : 0.8,
      },
    };
  }

  // ========== PUBLIC METHODS ==========
  public async generateResponse(
    query: string,
    mode: GameMode,
  ): Promise<AIResponse> {
    log.info(this.serviceName, `Generando respuesta para modo ${mode}`, {
      query: query.substring(0, 100),
      sessionId: this.sessionId,
    });

    try {
      const payload = this.buildPayload(query, mode);
      log.debug(this.serviceName, "Payload construido", {
        preset: payload.preset,
      });

      const data = await this.makeRequest<any>("/generate", payload);

      if (!data.ok) {
        throw new Error(data.message || "Error en la respuesta de la IA");
      }

      // Actualizar sessionId si es una nueva sesión
      if (data.id?.ses) {
        const oldSessionId = this.sessionId;
        this.sessionId = data.id.ses;
        log.info(
          this.serviceName,
          `Session ID actualizado: ${oldSessionId} → ${this.sessionId}`,
        );
      }

      log.success(this.serviceName, "Respuesta generada exitosamente", {
        outputLength: data.output?.length,
        sessionId: this.sessionId,
      });

      return {
        ok: true,
        output: data.output,
        sessionId: this.sessionId || "",
      };
    } catch (error) {
      log.error(this.serviceName, "Error generando respuesta", error);
      return {
        ok: false,
        output: "",
        sessionId: this.sessionId || "",
        error: error instanceof Error ? error.message : "Error desconocido",
      };
    }
  }

  public async resetSession(): Promise<boolean> {
    if (!this.sessionId) {
      console.log("⚠️ AI Service: No hay sesión para resetear");
      return true;
    }

    try {
      console.log(`🔄 AI Service: Reseteando sesión ${this.sessionId}`);

      const data = await this.makeRequest<any>(`/reset/${this.sessionId}`);

      if (data.ok) {
        console.log("✅ AI Service: Sesión reseteada correctamente");
        this.sessionId = null;
        return true;
      } else {
        throw new Error("Error al resetear sesión");
      }
    } catch (error) {
      console.error("❌ AI Service: Error al resetear sesión:", error);
      // Limpiar sessionId local independientemente del resultado
      this.sessionId = null;
      return false;
    }
  }

  public async startNewGame(mode: GameMode): Promise<AIResponse | null> {
    log.info(this.serviceName, `Iniciando nuevo juego en modo: ${mode}`);

    try {
      // Resetear sesión anterior si existe
      if (this.sessionId) {
        log.debug(
          this.serviceName,
          `Reseteando sesión anterior: ${this.sessionId}`,
        );
        await this.resetSession();
      }

      // Mensaje específico según el modo
      const initialMessage =
        mode === "yo_pienso"
          ? "Piensa en alguien... y empezamos cuando quieras."
          : "¡Hola! Estoy listo para adivinar lo que sea. ¿Ya tienes un personaje en mente?";

      const response = await this.generateResponse(initialMessage, mode);

      if (response.ok) {
        log.success(this.serviceName, "Juego iniciado exitosamente", {
          mode,
          sessionId: response.sessionId,
        });
      }

      return response;
    } catch (error) {
      log.error(this.serviceName, "Error en startNewGame", error);
      return null;
    }
  }

  public clearSession(): void {
    this.sessionId = null;
    console.log("🧹 AI Service: Sesión local limpiada");
  }
}

// ========== SINGLETON EXPORT ==========
export const aiService = AIService.getInstance();
