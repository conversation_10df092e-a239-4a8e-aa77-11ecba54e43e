import { log } from "./LogService";

// ========== TYPES ==========
export type GameMode = "yo_pienso" | "aura_piensa";

export interface AIResponse {
  ok: boolean;
  output: string;
  sessionId: string;
  error?: string;
  characterName?: string; // Nuevo campo para el personaje detectado
}

interface APIPayload {
  id: {
    ses?: string;
    clt: string;
    corr: string;
  };
  preset: string;
  query: string;
  prompt_params: {
    preamble: string;
  };
  model_params: {
    max_tokens: number;
    // temperature?: number;
  };
}

// ========== CONFIGURATION ==========
const API_CONFIG = {
  baseURL:
    import.meta.env.VITE_IA_API_URL ||
    "https://dev.dl2discovery.org/llm-api/v1/",
  apiKey:
    import.meta.env.VITE_IA_API_KEY || "b3df20aa-db3b-49ef-8d3b-4abfac8c1161",
  presets: {
    aura: import.meta.env.VITE_IA_PRESETID_AURA || "mapp-genigma_gpt4o_aura",
    user: import.meta.env.VITE_IA_PRESETID_USER || "mapp-genigma_gpt4o_user",
  },
};

// ========== GAME PROMPTS ==========
const GAME_PROMPTS = {
  yo_pienso: `
        Eres un juego de adivinanza estilo "Akinator". El usuario debe pensar en un personaje, actor, actriz u otra figura del mundo del entretenimiento (real o ficticio).

        Tu tarea es adivinar quién es haciendo preguntas que el usuario solo puede responder con: "Sí", "Es posible", "No", "No sé".

        Reglas del juego:
        - Solo tú haces preguntas. El usuario responde con una de las opciones anteriores.
        - Haz solo una pregunta por turno. No uses preguntas dobles ni abiertas.
        - No repitas preguntas ni hagas variantes de lo que ya sabes.
        - Lleva una lista interna de lo que has aprendido para no preguntar lo mismo.
        - Adapta tus preguntas estratégicamente según las respuestas del usuario.
        - Empieza con preguntas genéricas y así descartas más ideas de golpe.
        - Puedes hacer un máximo de 20 preguntas.
        - Cuando creas tener suficiente información, haz una suposición: "Creo que estás pensando en [nombre del personaje]. ¿Es correcto?"

        Ejemplos de preguntas válidas:
        - ¿Tu personaje aparece en películas?
        - ¿Es una persona real?
        - ¿Es mayor de 50 años?
        - ¿Trabaja principalmente en Hollywood?

        Ejemplos inválidos:
        - ¿Tu personaje es real o ficticio? (pregunta doble)
        - ¿Qué tipo de películas hace? (pregunta abierta)
    `,
  aura_piensa: `
        Eres Enygma, una IA que ha pensado en un personaje del mundo del entretenimiento. El usuario debe adivinarlo haciéndote preguntas.

        Tu tarea es:
        - Responder SOLO con "Sí", "No" o "No lo sé" a las preguntas del usuario.
        - Ser consistente con tus respuestas sobre el personaje que elegiste.
        - Mantener el misterio hasta que el usuario adivine correctamente.
        - No dar pistas adicionales, solo responder la pregunta específica.
        - Si el usuario hace una pregunta ambigua, responde "No lo sé".

        IMPORTANTE:
        1. Debes "pensar" en un personaje específico al inicio del juego y mantener todas tus respuestas coherentes con ese personaje.
        2. En tu PRIMERA respuesta del juego, debes incluir el nombre COMPLETO Y ESPECÍFICO del personaje que elegiste en el formato: "Personaje: [NOMBRE_COMPLETO]" al final de tu mensaje de bienvenida.
        3. El personaje DEBE ser una persona real con nombre y apellido, NO uses términos genéricos como "alguien del mundo del entretenimiento".
        4. Después de revelar el personaje en la primera respuesta, NUNCA más lo menciones hasta que el usuario lo adivine.

        Ejemplo de primera respuesta CORRECTA:
        "¡Perfecto! Ya he pensado en alguien del mundo del entretenimiento. Puedes empezar a hacerme preguntas y yo responderé solo con 'Sí', 'No' o 'No lo sé'. ¡A ver si puedes adivinarlo! Personaje: Leonardo DiCaprio"

        Ejemplo INCORRECTO (NO hagas esto):
        "¡Perfecto! Ya he pensado en alguien del mundo del entretenimiento. Personaje: alguien del mundo del entretenimiento"

        Ejemplos de respuestas posteriores válidas:
        - "Sí"
        - "No"
        - "No lo sé"

        Ejemplos inválidos:
        - "Sí, aparece en muchas películas" (información extra)
        - "No, pero sí en series" (información extra)
        - "Depende de lo que consideres famoso" (explicación)
    `,
};

// ========== AI SERVICE ==========
class AIService {
  private static instance: AIService;
  private sessionId: string | null = null;
  private serviceName = "ai";

  private constructor() {}

  public static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  // ========== GETTERS ==========
  public getSessionId(): string | null {
    return this.sessionId;
  }

  public getPresetForMode(mode: GameMode): string {
    switch (mode) {
      case "yo_pienso":
        return API_CONFIG.presets.user; // Usuario piensa, IA adivina
      case "aura_piensa":
        return API_CONFIG.presets.aura; // Aura piensa, usuario adivina
      default:
        return API_CONFIG.presets.user;
    }
  }

  public getApiConfig() {
    return { ...API_CONFIG };
  }

  // ========== PRIVATE METHODS ==========
  private async makeRequest<T>(endpoint: string, payload?: any): Promise<T> {
    try {
      const url = `${API_CONFIG.baseURL}${endpoint}`;
      const options: RequestInit = {
        headers: {
          "Content-Type": "application/json",
          "X-Api-Key": API_CONFIG.apiKey,
        },
      };

      if (payload) {
        options.method = "POST";
        options.body = JSON.stringify(payload);
      } else {
        options.method = "GET";
      }

      const response = await fetch(url, options);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();
      return data as T;
    } catch (error) {
      if (import.meta.env.MODE === "development") {
        console.error(`🔴 AI API ERROR: ${error}`);
      }
      throw new Error("Error al conectar con la API de IA");
    }
  }

  private buildPayload(query: string, mode: GameMode): APIPayload {
    const preset = this.getPresetForMode(mode);

    return {
      id: {
        ses: this.sessionId || undefined,
        clt: "aura-game-client",
        corr: `game-${Date.now()}`,
      },
      preset,
      query,
      prompt_params: {
        preamble: GAME_PROMPTS[mode],
      },
      model_params: {
        max_tokens: mode === "aura_piensa" ? 50 : 200,
        // temperature: mode === "aura_piensa" ? 0.3 : 0.8,
      },
    };
  }

  // ========== PRIVATE METHODS - CHARACTER DETECTION ==========
  private extractCharacterFromResponse(output: string, mode: GameMode): string | undefined {
    if (mode !== "aura_piensa") return undefined;

    // Buscar patrones comunes donde la IA podría revelar el personaje
    const patterns = [
      // Patrón principal esperado: "Personaje: [NOMBRE]"
      /Personaje:\s*([^.!?\n]+)/i,
      // Patrones alternativos por si la IA usa otras formas
      /(?:he pensado en|elegido|seleccionado|mi personaje es)\s*:?\s*([^.!?\n]+)/i,
      /(?:character):\s*([^.!?\n]+)/i,
      /(?:estoy pensando en)\s*([^.!?\n]+)/i,
      /(?:mi elección es)\s*([^.!?\n]+)/i,
      /(?:he elegido a)\s*([^.!?\n]+)/i,
    ];

    for (const pattern of patterns) {
      const match = output.match(pattern);
      if (match && match[1]) {
        const character = match[1].trim();
        // Limpiar caracteres especiales al final
        const cleanCharacter = character.replace(/["""''`]+$/, '').trim();
        log.info(this.serviceName, `🎭 Personaje detectado: "${cleanCharacter}"`);
        return cleanCharacter;
      }
    }

    log.warn(this.serviceName, "🎭 No se pudo detectar personaje en la respuesta", {
      output: output.substring(0, 200) + "...",
    });
    return undefined;
  }

  private isCharacterSpecific(character: string): boolean {
    // Verificar si el personaje es específico o genérico
    const genericTerms = [
      "alguien del mundo del entretenimiento",
      "una persona famosa",
      "un actor",
      "una actriz",
      "un personaje",
      "alguien famoso",
      "una celebridad",
      "un artista",
    ];

    const lowerCharacter = character.toLowerCase();
    return !genericTerms.some(term => lowerCharacter.includes(term));
  }

  private saveCharacterToStorage(character: string): void {
    try {
      localStorage.setItem("enygma_current_character", character);
      localStorage.setItem("enygma_character_timestamp", new Date().toISOString());
      log.debug(this.serviceName, `💾 Personaje guardado en localStorage: ${character}`);
    } catch (error) {
      log.warn(this.serviceName, "No se pudo guardar el personaje en localStorage", error);
    }
  }

  private async requestSpecificCharacter(): Promise<void> {
    if (!this.sessionId) {
      log.warn(this.serviceName, "⚠️ No hay sesión activa para solicitar personaje específico");
      return;
    }

    try {
      log.info(this.serviceName, "🔄 Solicitando personaje específico...");

      const specificQuery = "Por favor, dime exactamente el nombre completo del personaje en el que estás pensando. Solo responde con el nombre, nada más.";

      const payload = this.buildPayload(specificQuery, "aura_piensa");
      const data = await this.makeRequest<any>("/generate", payload);

      if (data.ok && data.output) {
        // Extraer el nombre específico de la respuesta
        const specificCharacter = this.extractSpecificCharacterName(data.output);

        if (specificCharacter && this.isCharacterSpecific(specificCharacter)) {
          this.saveCharacterToStorage(specificCharacter);
          log.success(this.serviceName, `✅ Personaje específico obtenido y guardado: "${specificCharacter}"`);

          // Disparar evento personalizado para notificar a los componentes
          window.dispatchEvent(new CustomEvent('characterUpdated', {
            detail: { character: specificCharacter }
          }));
        } else {
          log.warn(this.serviceName, `⚠️ La segunda respuesta tampoco fue específica: "${data.output}"`);
        }
      }
    } catch (error) {
      log.error(this.serviceName, "❌ Error solicitando personaje específico", error);
    }
  }

  private extractSpecificCharacterName(output: string): string | undefined {
    // Limpiar la respuesta para obtener solo el nombre
    let cleanOutput = output.trim();

    // Remover frases comunes de respuesta
    const phrasesToRemove = [
      /^(el personaje es|estoy pensando en|mi personaje es|es)\s*/i,
      /^(sí,?\s*)?/i,
      /\.$/, // Punto final
    ];

    phrasesToRemove.forEach(phrase => {
      cleanOutput = cleanOutput.replace(phrase, '');
    });

    cleanOutput = cleanOutput.trim();

    // Si la respuesta es muy larga, probablemente no es solo un nombre
    if (cleanOutput.length > 50) {
      log.warn(this.serviceName, `⚠️ Respuesta muy larga para ser un nombre: "${cleanOutput}"`);
      return undefined;
    }

    // Si contiene palabras como "no puedo", "no debo", etc., no es válida
    const invalidPhrases = ["no puedo", "no debo", "no está permitido", "lo siento"];
    if (invalidPhrases.some(phrase => cleanOutput.toLowerCase().includes(phrase))) {
      log.warn(this.serviceName, `⚠️ Respuesta indica negativa: "${cleanOutput}"`);
      return undefined;
    }

    return cleanOutput || undefined;
  }

  // ========== PUBLIC METHODS ==========
  public async generateResponse(
    query: string,
    mode: GameMode,
  ): Promise<AIResponse> {
    log.info(this.serviceName, `Generando respuesta para modo ${mode}`, {
      query: query.substring(0, 100),
      sessionId: this.sessionId,
    });

    try {
      const payload = this.buildPayload(query, mode);
      log.debug(this.serviceName, "Payload construido", {
        preset: payload.preset,
      });

      const data = await this.makeRequest<any>("/generate", payload);

      if (!data.ok) {
        throw new Error(data.message || "Error en la respuesta de la IA");
      }

      // Actualizar sessionId si es una nueva sesión
      if (data.id?.ses) {
        const oldSessionId = this.sessionId;
        this.sessionId = data.id.ses;
        log.info(
          this.serviceName,
          `Session ID actualizado: ${oldSessionId} → ${this.sessionId}`,
        );
      }

      // Detectar y guardar personaje si es modo aura_piensa
      let characterName: string | undefined;
      if (mode === "aura_piensa") {
        characterName = this.extractCharacterFromResponse(data.output, mode);
        if (characterName) {
          // Verificar si el personaje es específico
          if (this.isCharacterSpecific(characterName)) {
            this.saveCharacterToStorage(characterName);
            log.success(this.serviceName, `✅ Personaje específico guardado: "${characterName}"`);
          } else {
            log.warn(this.serviceName, `⚠️ Personaje genérico detectado: "${characterName}". Solicitando especificación...`);

            // Programar segunda petición para obtener personaje específico
            setTimeout(async () => {
              try {
                await this.requestSpecificCharacter();
              } catch (error) {
                log.error(this.serviceName, "❌ Error en segunda petición para personaje específico", error);
              }
            }, 3000);
          }
        }
      }

      log.success(this.serviceName, "Respuesta generada exitosamente", {
        outputLength: data.output?.length,
        sessionId: this.sessionId,
        characterDetected: characterName || "No detectado",
      });

      return {
        ok: true,
        output: data.output,
        sessionId: this.sessionId || "",
        characterName,
      };
    } catch (error) {
      log.error(this.serviceName, "Error generando respuesta", error);
      return {
        ok: false,
        output: "",
        sessionId: this.sessionId || "",
        error: error instanceof Error ? error.message : "Error desconocido",
      };
    }
  }

  public async resetSession(): Promise<boolean> {
    if (!this.sessionId) {
      console.log("⚠️ AI Service: No hay sesión para resetear");
      return true;
    }

    try {
      console.log(`🔄 AI Service: Reseteando sesión ${this.sessionId}`);

      const data = await this.makeRequest<any>(`/reset/${this.sessionId}`);

      if (data.ok) {
        console.log("✅ AI Service: Sesión reseteada correctamente");
        this.sessionId = null;

        // Limpiar personaje guardado al resetear sesión
        this.clearStoredCharacter();

        return true;
      } else {
        throw new Error("Error al resetear sesión");
      }
    } catch (error) {
      console.error("❌ AI Service: Error al resetear sesión:", error);
      // Limpiar sessionId local independientemente del resultado
      this.sessionId = null;
      this.clearStoredCharacter();
      return false;
    }
  }

  private clearStoredCharacter(): void {
    try {
      localStorage.removeItem("enygma_current_character");
      localStorage.removeItem("enygma_character_timestamp");
      log.debug(this.serviceName, "🗑️ Personaje eliminado del localStorage");
    } catch (error) {
      log.warn(this.serviceName, "No se pudo limpiar el personaje del localStorage", error);
    }
  }

  // ========== UTILIDADES PÚBLICAS ==========
  public getStoredCharacter(): { character: string | null; timestamp: string | null } {
    try {
      const character = localStorage.getItem("enygma_current_character");
      const timestamp = localStorage.getItem("enygma_character_timestamp");
      return { character, timestamp };
    } catch (error) {
      log.warn(this.serviceName, "No se pudo leer el personaje del localStorage", error);
      return { character: null, timestamp: null };
    }
  }

  public async startNewGame(mode: GameMode): Promise<AIResponse | null> {
    log.info(this.serviceName, `Iniciando nuevo juego en modo: ${mode}`);

    try {
      // Resetear sesión anterior si existe
      if (this.sessionId) {
        log.debug(
          this.serviceName,
          `Reseteando sesión anterior: ${this.sessionId}`,
        );
        await this.resetSession();
      }

      // Mensaje específico según el modo
      const initialMessage =
        mode === "yo_pienso"
          ? "Piensa en alguien... y empezamos cuando quieras."
          : "¡Hola! Estoy listo para adivinar lo que sea. ¿Ya tienes un personaje en mente?";

      const response = await this.generateResponse(initialMessage, mode);

      if (response.ok) {
        log.success(this.serviceName, "Juego iniciado exitosamente", {
          mode,
          sessionId: response.sessionId,
        });
      }

      return response;
    } catch (error) {
      log.error(this.serviceName, "Error en startNewGame", error);
      return null;
    }
  }

  public clearSession(): void {
    this.sessionId = null;
    console.log("🧹 AI Service: Sesión local limpiada");
  }
}

// ========== SINGLETON EXPORT ==========
export const aiService = AIService.getInstance();
