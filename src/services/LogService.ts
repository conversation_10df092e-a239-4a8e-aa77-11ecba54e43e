// ========== TYPES ==========
export type LogLevel = "debug" | "info" | "warn" | "error" | "success";

export interface LogEntry {
  id: string;
  timestamp: Date;
  level: LogLevel;
  service: string;
  message: string;
  data?: any;
  stack?: string;
}

export interface LogFilter {
  levels?: LogLevel[];
  services?: string[];
  search?: string;
}

// ========== LOG SERVICE ==========
class LogService {
  private static instance: LogService;
  private logs: LogEntry[] = [];
  private listeners: Set<(logs: LogEntry[]) => void> = new Set();
  private maxLogs: number = 1000;
  private isEnabled: boolean = true;

  private constructor() {
    // Configurar según entorno
    this.isEnabled = import.meta.env.MODE === "development";

    // Interceptar console.log, console.error, etc.
    this.interceptConsole();
  }

  public static getInstance(): LogService {
    if (!LogService.instance) {
      LogService.instance = new LogService();
    }
    return LogService.instance;
  }

  // ========== CONFIGURACIÓN ==========
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }

  public setMaxLogs(max: number): void {
    this.maxLogs = max;
    this.trimLogs();
  }

  // ========== LOGGING METHODS ==========
  public debug(service: string, message: string, data?: any): void {
    this.addLog("debug", service, message, data);
  }

  public info(service: string, message: string, data?: any): void {
    this.addLog("info", service, message, data);
  }

  public warn(service: string, message: string, data?: any): void {
    this.addLog("warn", service, message, data);
  }

  public error(service: string, message: string, error?: any): void {
    const stack = error instanceof Error ? error.stack : undefined;
    this.addLog("error", service, message, error, stack);
  }

  public success(service: string, message: string, data?: any): void {
    this.addLog("success", service, message, data);
  }

  // ========== PRIVATE METHODS ==========
  private addLog(
    level: LogLevel,
    service: string,
    message: string,
    data?: any,
    stack?: string,
  ): void {
    if (!this.isEnabled) return;

    const logEntry: LogEntry = {
      id: this.generateId(),
      timestamp: new Date(),
      level,
      service,
      message,
      data,
      stack,
    };

    this.logs.unshift(logEntry); // Añadir al principio
    this.trimLogs();
    this.notifyListeners();

    // También log a console normal
    this.logToConsole(logEntry);
  }

  private logToConsole(entry: LogEntry): void {
    const timestamp = entry.timestamp.toLocaleTimeString();
    const prefix = `[${timestamp}] [${entry.service.toUpperCase()}]`;
    const message = `${prefix} ${entry.message}`;

    switch (entry.level) {
      case "debug":
        console.log(`🔍 ${message}`, entry.data || "");
        break;
      case "info":
        console.info(`ℹ️ ${message}`, entry.data || "");
        break;
      case "warn":
        console.warn(`⚠️ ${message}`, entry.data || "");
        break;
      case "error":
        console.error(`❌ ${message}`, entry.data || "", entry.stack || "");
        break;
      case "success":
        console.log(`✅ ${message}`, entry.data || "");
        break;
    }
  }

  private trimLogs(): void {
    if (this.logs.length > this.maxLogs) {
      this.logs = this.logs.slice(0, this.maxLogs);
    }
  }

  private notifyListeners(): void {
    this.listeners.forEach((listener) => {
      try {
        listener([...this.logs]); // Copia del array
      } catch (error) {
        console.error("Error en listener de logs:", error);
      }
    });
  }

  private generateId(): string {
    return `log-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  private interceptConsole(): void {
    // Guardar métodos originales
    const originalLog = console.log;
    const originalError = console.error;
    const originalWarn = console.warn;
    const originalInfo = console.info;

    // Interceptar console.log
    console.log = (...args) => {
      // Llamar al método original
      originalLog.apply(console, args);

      // Si el mensaje ya tiene nuestro formato, no duplicar
      const message = args.join(" ");
      if (!message.includes("[") && this.isEnabled) {
        this.addLog(
          "debug",
          "console",
          message,
          args.length > 1 ? args.slice(1) : undefined,
        );
      }
    };

    // Interceptar console.error
    console.error = (...args) => {
      originalError.apply(console, args);

      const message = args.join(" ");
      if (!message.includes("[") && this.isEnabled) {
        this.addLog(
          "error",
          "console",
          message,
          args.length > 1 ? args.slice(1) : undefined,
        );
      }
    };

    // Interceptar console.warn
    console.warn = (...args) => {
      originalWarn.apply(console, args);

      const message = args.join(" ");
      if (!message.includes("[") && this.isEnabled) {
        this.addLog(
          "warn",
          "console",
          message,
          args.length > 1 ? args.slice(1) : undefined,
        );
      }
    };

    // Interceptar console.info
    console.info = (...args) => {
      originalInfo.apply(console, args);

      const message = args.join(" ");
      if (!message.includes("[") && this.isEnabled) {
        this.addLog(
          "info",
          "console",
          message,
          args.length > 1 ? args.slice(1) : undefined,
        );
      }
    };
  }

  // ========== PUBLIC METHODS ==========
  public getLogs(filter?: LogFilter): LogEntry[] {
    let filteredLogs = [...this.logs];

    if (filter) {
      if (filter.levels) {
        filteredLogs = filteredLogs.filter((log) =>
          filter.levels!.includes(log.level),
        );
      }

      if (filter.services) {
        filteredLogs = filteredLogs.filter((log) =>
          filter.services!.includes(log.service),
        );
      }

      if (filter.search) {
        const searchTerm = filter.search.toLowerCase();
        filteredLogs = filteredLogs.filter(
          (log) =>
            log.message.toLowerCase().includes(searchTerm) ||
            log.service.toLowerCase().includes(searchTerm),
        );
      }
    }

    return filteredLogs;
  }

  public addListener(listener: (logs: LogEntry[]) => void): () => void {
    this.listeners.add(listener);

    // Enviar logs actuales de forma asíncrona para evitar setState durante render
    setTimeout(() => {
      if (this.listeners.has(listener)) {
        listener([...this.logs]);
      }
    }, 0);

    // Retornar función para remover listener
    return () => {
      this.listeners.delete(listener);
    };
  }

  public clearLogs(): void {
    this.logs = [];
    this.notifyListeners();
  }

  public exportLogs(): string {
    return JSON.stringify(this.logs, null, 2);
  }

  public getStats(): object {
    const serviceStats = this.logs.reduce(
      (acc, log) => {
        acc[log.service] = (acc[log.service] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    const levelStats = this.logs.reduce(
      (acc, log) => {
        acc[log.level] = (acc[log.level] || 0) + 1;
        return acc;
      },
      {} as Record<LogLevel, number>,
    );

    return {
      totalLogs: this.logs.length,
      maxLogs: this.maxLogs,
      enabled: this.isEnabled,
      listeners: this.listeners.size,
      serviceStats,
      levelStats,
      oldestLog: this.logs[this.logs.length - 1]?.timestamp,
      newestLog: this.logs[0]?.timestamp,
    };
  }
}

// ========== SINGLETON EXPORT ==========
export const logService = LogService.getInstance();

// ========== HELPER FUNCTIONS ==========
export const log = {
  debug: (service: string, message: string, data?: any) =>
    logService.debug(service, message, data),
  info: (service: string, message: string, data?: any) =>
    logService.info(service, message, data),
  warn: (service: string, message: string, data?: any) =>
    logService.warn(service, message, data),
  error: (service: string, message: string, error?: any) =>
    logService.error(service, message, error),
  success: (service: string, message: string, data?: any) =>
    logService.success(service, message, data),
};
