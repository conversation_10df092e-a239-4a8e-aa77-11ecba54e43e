.
├── docs
│   ├── figma.md
│   ├── file-system.md
│   └── prompt.md
├── .env
├── eslint.config.js
├── .gitignore
├── index.html
├── package.json
├── package-lock.json
├── public
│   ├── assets
│   │   ├── favicon.png
│   │   ├── fonts
│   │   │   ├── OnAir-BlackItalic.ttf
│   │   │   ├── OnAir-Black.ttf
│   │   │   ├── OnAir-BoldItalic.ttf
│   │   │   ├── OnAir-Bold.ttf
│   │   │   ├── OnAir-Italic.ttf
│   │   │   ├── OnAir-LightItalic.ttf
│   │   │   ├── OnAir-Light.ttf
│   │   │   ├── OnAirOutlineOne.ttf
│   │   │   ├── OnAirOutlineThree.ttf
│   │   │   ├── OnAirOutlineTwo.ttf
│   │   │   └── OnAir-Regular.ttf
│   │   └── game
│   │       ├── background.png
│   │       ├── book_1.png
│   │       ├── book_2.png
│   │       ├── book.png
│   │       └── enygma.png
│   ├── game-rules.json
│   └── sdk.js
├── README.md
├── src
│   ├── App.scss
│   ├── App.tsx
│   ├── assets
│   │   └── images
│   │       └── background.png
│   ├── components
│   │   ├── CookieConsentBanner.tsx
│   │   ├── game
│   │   │   ├── GameRules.scss
│   │   │   ├── GameRules.tsx
│   │   │   ├── GameStepsContainer.tsx
│   │   │   └── steps
│   │   │       ├── InitGameStep.tsx
│   │   │       ├── PlayingGameStep.tsx
│   │   │       └── ResultsGameStep.tsx
│   │   ├── GlobalLogs.tsx
│   │   ├── images
│   │   │   ├── auraThink.tsx
│   │   │   └── meThink.tsx
│   │   ├── Loader.scss
│   │   └── Loader.tsx
│   ├── contexts
│   │   ├── AIContext.tsx
│   │   ├── AppContext.tsx
│   │   ├── AudioContext.tsx
│   │   ├── EnygmaGameContext.tsx
│   │   ├── GameContext.tsx
│   │   ├── GameOrchestratorContext.tsx
│   │   ├── MHCContext.tsx
│   │   ├── MovistarPlusContext.tsx
│   │   ├── SpeechContext.tsx
│   │   ├── SpeechInputContext.tsx
│   │   ├── SpeechOutputContext.tsx
│   │   └── TranscriptionContext.tsx
│   ├── index.scss
│   ├── main.tsx
│   ├── services
│   │   ├── AIService.ts
│   │   ├── _helpersService.ts
│   │   ├── LogService.ts
│   │   ├── MovistarPlusService.ts
│   │   ├── SpeechService.ts
│   │   └── TranscriptionService.ts
│   ├── _testing
│   │   ├── MenuDebugDialog.tsx
│   │   ├── TestIA.tsx
│   │   ├── TestMovistarPlus.tsx
│   │   ├── TestSpeech.tsx
│   │   └── TestTranscription.tsx
│   ├── _variables.scss
│   └── vite-env.d.ts
├── tsconfig.app.json
├── tsconfig.json
├── tsconfig.node.json
└── vite.config.ts

16 directories, 75 files
